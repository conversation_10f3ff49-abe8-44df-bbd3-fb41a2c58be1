import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import LocalStorageService from '../../services/LocalStorageService';
import {
  FaQrcode,
  FaDownload,
  FaTable,
  FaPrint,
  FaCopy,
  FaCheck,
  FaStore
} from 'react-icons/fa';
import QRCode from 'qrcode';

const ZoneAdminQRCodeGenerator = () => {
  const { user } = useSelector((state) => state.auth);
  const { zoneId } = useParams();
  const [tables, setTables] = useState([]);
  const [selectedTables, setSelectedTables] = useState([]);
  const [qrCodes, setQrCodes] = useState({});
  const [loading, setLoading] = useState(false);
  const [copiedUrl, setCopiedUrl] = useState(null);
  const [maxTables, setMaxTables] = useState(50); // Set by Super Admin
  const [zoneData, setZoneData] = useState(null);

  useEffect(() => {
    // Load zone data to get table limit
    const loadZoneData = () => {
      const zone = LocalStorageService.getZoneProfile(zoneId);
      if (zone) {
        setZoneData(zone);
        setMaxTables(zone.maxTables || 50);
      }
    };

    if (zoneId) {
      loadZoneData();
    }
  }, [zoneId]);

  useEffect(() => {
    // Load existing zone tables or create default ones based on maxTables limit
    if (maxTables > 0 && zoneId) {
      const savedTables = localStorage.getItem(`zone_tables_${zoneId}`);
      if (savedTables) {
        const parsedTables = JSON.parse(savedTables);
        // Ensure we don't exceed the maxTables limit
        const limitedTables = parsedTables.slice(0, maxTables);
        setTables(limitedTables);

        // If we had to trim tables, save the updated list
        if (limitedTables.length < parsedTables.length) {
          localStorage.setItem(`zone_tables_${zoneId}`, JSON.stringify(limitedTables));
        }
      } else {
        // Create default zone tables up to maxTables limit
        const defaultTables = Array.from({ length: maxTables }, (_, i) => ({
          id: i + 1,
          number: i + 1,
          status: 'active',
          qrGenerated: false,
          lastGenerated: null,
          sessionId: null,
          location: `Table ${i + 1}` // Can be customized later
        }));
        setTables(defaultTables);
        localStorage.setItem(`zone_tables_${zoneId}`, JSON.stringify(defaultTables));
      }
    }
  }, [zoneId, maxTables]);

  const generateSecureToken = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  };

  const generateQRData = (tableNumber) => {
    const qrData = {
      zoneId,
      zoneName: user?.zoneName,
      tableNumber,
      sessionId: generateSecureToken(),
      createdAt: new Date().toISOString(),
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      version: '1.0'
    };

    // Generate zone-wide table URL (customers choose shop after scanning)
    const tableUrl = `${window.location.origin}/tableserve/zone/${zoneId}/table/${tableNumber}/shops`;

    return {
      data: qrData,
      url: tableUrl
    };
  };

  const generateQRCode = async (tableNumber) => {
    try {
      const qrInfo = generateQRData(tableNumber);

      // Generate QR code image
      const qrCodeDataURL = await QRCode.toDataURL(qrInfo.url, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      });

      return {
        ...qrInfo,
        qrCodeImage: qrCodeDataURL
      };
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw error;
    }
  };

  const handleGenerateSelected = async () => {
    if (selectedTables.length === 0) return;

    setLoading(true);
    const newQrCodes = { ...qrCodes };

    try {
      for (const tableNumber of selectedTables) {
        const qrInfo = await generateQRCode(tableNumber);
        newQrCodes[tableNumber] = qrInfo;

        // Update table status
        setTables(prev => prev.map(table =>
          table.number === tableNumber
            ? { ...table, qrGenerated: true, lastGenerated: new Date().toISOString() }
            : table
        ));
      }

      setQrCodes(newQrCodes);

      // Save updated tables
      const updatedTables = tables.map(table =>
        selectedTables.includes(table.number)
          ? { ...table, qrGenerated: true, lastGenerated: new Date().toISOString() }
          : table
      );
      localStorage.setItem(`zone_tables_${zoneId}`, JSON.stringify(updatedTables));

    } catch (error) {
      console.error('Error generating QR codes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableSelect = (tableNumber) => {
    setSelectedTables(prev =>
      prev.includes(tableNumber)
        ? prev.filter(t => t !== tableNumber)
        : [...prev, tableNumber]
    );
  };

  const copyToClipboard = async (url, tableNumber) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(tableNumber);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  const downloadQRCode = (qrCodeImage, tableNumber) => {
    const link = document.createElement('a');
    link.download = `zone-${zoneId}-table-${tableNumber}-qr.png`;
    link.href = qrCodeImage;
    link.click();
  };



  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-fredoka text-theme-text-primary mb-2">
          Zone QR Code Generator
        </h1>
        <p className="text-theme-text-secondary font-raleway">
          Generate QR codes for all tables in your zone. Customers scan to choose from available shops.
        </p>
        <p className="text-theme-text-tertiary font-raleway text-sm mt-1">
          Table Limit: {tables.length} / {maxTables} tables (Set by Super Admin)
        </p>
      </div>



      {/* Table Selection */}
      <div className="admin-card rounded-lg p-6 mb-6">
        <h2 className="text-lg font-fredoka text-theme-text-primary mb-4 flex items-center">
          <FaTable className="mr-2" />
          Select Tables for QR Generation
        </h2>

        <div className="grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2 mb-4 max-h-96 overflow-y-auto">
          {tables.map(table => (
            <motion.button
              key={table.number}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleTableSelect(table.number)}
              className={`
                relative p-2 rounded-lg border-2 transition-all font-raleway font-semibold text-sm
                ${selectedTables.includes(table.number)
                  ? 'border-accent bg-accent text-white'
                  : 'border-theme-border bg-theme-surface text-theme-text-primary hover:border-accent'
                }
                ${table.qrGenerated ? 'ring-2 ring-green-500 ring-opacity-50' : ''}
              `}
            >
              {table.number}
              {table.qrGenerated && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"></div>
              )}
            </motion.button>
          ))}
        </div>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setSelectedTables(tables.map(t => t.number))}
            className="px-4 py-2 bg-theme-surface border border-theme-border rounded-lg text-theme-text-primary hover:bg-accent hover:text-white transition-colors font-raleway"
          >
            Select All
          </button>
          <button
            onClick={() => setSelectedTables([])}
            className="px-4 py-2 bg-theme-surface border border-theme-border rounded-lg text-theme-text-primary hover:bg-red-500 hover:text-white transition-colors font-raleway"
          >
            Clear Selection
          </button>
          <button
            onClick={handleGenerateSelected}
            disabled={selectedTables.length === 0 || loading}
            className="px-6 py-2 bg-accent text-white rounded-lg hover:bg-accent/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-raleway font-semibold flex items-center"
          >
            <FaQrcode className="mr-2" />
            {loading ? 'Generating...' : `Generate QR (${selectedTables.length})`}
          </button>
        </div>
      </div>

      {/* Generated QR Codes */}
      {Object.keys(qrCodes).length > 0 && (
        <div className="admin-card rounded-lg p-6">
          <h2 className="text-lg font-fredoka text-theme-text-primary mb-4">
            Generated Zone QR Codes
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Object.entries(qrCodes).map(([tableNumber, qrInfo]) => (
              <motion.div
                key={tableNumber}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border border-theme-border rounded-lg p-4 bg-theme-surface"
              >
                <div className="text-center mb-3">
                  <h3 className="font-fredoka text-theme-text-primary text-lg">
                    Zone Table {tableNumber}
                  </h3>
                </div>

                <div className="flex justify-center mb-3">
                  <img
                    src={qrInfo.qrCodeImage}
                    alt={`QR Code for Zone Table ${tableNumber}`}
                    className="w-32 h-32 border border-theme-border rounded"
                  />
                </div>

                <div className="space-y-2">
                  <div className="text-xs text-theme-text-secondary font-raleway break-all">
                    {qrInfo.url}
                  </div>

                  <div className="flex gap-1">
                    <button
                      onClick={() => copyToClipboard(qrInfo.url, tableNumber)}
                      className="flex-1 px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors flex items-center justify-center"
                    >
                      {copiedUrl === parseInt(tableNumber) ? <FaCheck className="mr-1" /> : <FaCopy className="mr-1" />}
                      {copiedUrl === parseInt(tableNumber) ? 'Copied!' : 'Copy'}
                    </button>
                    <button
                      onClick={() => downloadQRCode(qrInfo.qrCodeImage, tableNumber)}
                      className="flex-1 px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors flex items-center justify-center"
                    >
                      <FaDownload className="mr-1" />
                      Download
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ZoneAdminQRCodeGenerator;
