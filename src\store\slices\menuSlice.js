import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Mock data and localStorage functions
const getMenuFromStorage = (restaurantId) => {
  const stored = localStorage.getItem(`menu_${restaurantId}`);
  return stored ? JSON.parse(stored) : { categories: [], items: [] };
};

const saveMenuToStorage = (restaurantId, menu) => {
  localStorage.setItem(`menu_${restaurantId}`, JSON.stringify(menu));
};

export const fetchMenuByRestaurant = createAsyncThunk(
  'menu/fetchMenuByRestaurant',
  async (restaurantId, { rejectWithValue }) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      const menu = getMenuFromStorage(restaurantId);
      return menu;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const createMenuItem = createAsyncThunk(
  'menu/createMenuItem',
  async ({ restaurantId, itemData }, { rejectWithValue }) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      const menu = getMenuFromStorage(restaurantId);
      const newItem = {
        id: `item-${Date.now()}`,
        ...itemData,
      };
      const updatedMenu = {
        ...menu,
        items: [...menu.items, newItem],
      };
      saveMenuToStorage(restaurantId, updatedMenu);
      return newItem;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const menuSlice = createSlice({
  name: 'menu',
  initialState: {
    categories: [],
    items: [],
    loading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateItemAvailability: (state, action) => {
      const { itemId, available } = action.payload;
      const item = state.items.find(item => item.id === itemId);
      if (item) {
        item.available = available;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMenuByRestaurant.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchMenuByRestaurant.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload.categories;
        state.items = action.payload.items;
      })
      .addCase(fetchMenuByRestaurant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(createMenuItem.pending, (state) => {
        state.loading = true;
      })
      .addCase(createMenuItem.fulfilled, (state, action) => {
        state.items.push(action.payload);
        state.loading = false;
      })
      .addCase(createMenuItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, updateItemAvailability } = menuSlice.actions;
export default menuSlice.reducer;