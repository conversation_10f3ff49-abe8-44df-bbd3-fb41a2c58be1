import { useParams, useNavigate } from 'react-router-dom';
import { FaShoppingCart, FaUserCircle } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useContext } from 'react';
import { CartContext } from '../../context/CartContext.jsx';

const UserNavbar = () => {
    const { restaurantName, userId } = useParams();
    const navigate = useNavigate();
    const { cartItemCount } = useContext(CartContext);

    const handleViewCart = () => {
        navigate(`/tableserve/${restaurantName}/${userId}/cart`);
    };

    return (
        <div className="flex justify-between items-center p-4 bg-accent mb-6">
            <div>
                <h1 className="text-xl font-bold text-white capitalize">{restaurantName.replace(/-/g, ' ')} Menu</h1>
            </div>
            <div className="flex items-center space-x-4">
                <motion.button
                    whileTap={{ scale: 0.9 }}
                    onClick={handleViewCart}
                    className="relative p-2 bg-white rounded-full shadow-lg"
                >
                    <FaShoppingCart className="text-xl text-accent" />
                    <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                        {cartItemCount}
                    </span>
                </motion.button>
                <motion.button className='rounded-full shadow-lg' whileTap={{ scale: 0.9 }}
                    onClick={() => navigate(`/tableserve/${restaurantName}/${userId}/profile`)}>
                    <FaUserCircle className="text-4xl text-white" />
                </motion.button>
            </div>
        </div>
    );
};

export default UserNavbar;