import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaCheckCircle } from 'react-icons/fa';

const OrderSuccessScreen = () => {
  const { restaurantName, userId } = useParams();
  const navigate = useNavigate();

  const handleTrackOrder = () => {
    navigate(`/tableserve/${restaurantName}/${userId}/order-tracking`);
  };

  const handlePlaceNewOrder = () => {
    navigate(`/tableserve/${restaurantName}/${userId}/home`);
  };

  return (
    <div className="min-h-screen bg-accent text-text-main flex flex-col items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, type: 'spring', stiffness: 100 }}
        className="w-full max-w-md bg-white rounded-2xl p-6 shadow-lg backdrop-blur-md text-center"
      >
        <FaCheckCircle className="text-green-500 text-6xl mx-auto mb-4" />
        <h1 className="text-3xl font-fredoka text-accent mb-2">Order Placed Successfully!</h1>
        <p className="text-black text-lg mb-6">Your order is being prepared.</p>

        <div className="space-y-4">
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={handleTrackOrder}
            className="w-full bg-accent text-text-main py-3 rounded-2xl font-fredoka text-lg shadow-lg
                       hover:bg-hover-shade transition-colors duration-300"
          >
            Track Order
          </motion.button>
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={handlePlaceNewOrder}
            className="w-full bg-white  text-accent border-2 border-accent py-3 rounded-2xl font-fredoka text-lg shadow-lg
                       hover:bg-accent hover:text-white transition-colors duration-300"
          >
            Place Another Order
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

export default OrderSuccessScreen;