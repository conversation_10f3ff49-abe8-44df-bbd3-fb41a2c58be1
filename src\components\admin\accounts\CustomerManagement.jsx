import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FaUsers, 
  FaSearch,
  FaFilter,
  FaDownload,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaShoppingCart,
  FaStar,
  FaEye,
  FaBan
} from 'react-icons/fa';
import SuperAdminLayout from '../SuperAdminLayout';

const CustomerManagement = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock customer data
  const mockCustomers = [
    {
      id: 1,
      name: '<PERSON>',
      phone: '******-0123',
      email: '<EMAIL>',
      location: 'New York, NY',
      totalOrders: 45,
      totalSpent: 1250.75,
      avgRating: 4.8,
      lastOrder: '2024-01-20',
      joinDate: '2023-06-15',
      status: 'active',
      favoriteZone: 'Downtown Food Street'
    },
    {
      id: 2,
      name: '<PERSON>',
      phone: '******-0124',
      email: '<EMAIL>',
      location: 'Los Angeles, CA',
      totalOrders: 32,
      totalSpent: 890.50,
      avgRating: 4.6,
      lastOrder: '2024-01-19',
      joinDate: '2023-08-22',
      status: 'active',
      favoriteZone: 'Riverside Food Court'
    },
    {
      id: 3,
      name: 'Mike Wilson',
      phone: '******-0125',
      email: '<EMAIL>',
      location: 'Chicago, IL',
      totalOrders: 18,
      totalSpent: 425.25,
      avgRating: 4.2,
      lastOrder: '2024-01-15',
      joinDate: '2023-11-10',
      status: 'inactive',
      favoriteZone: 'Campus Food Hub'
    }
  ];

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setCustomers(mockCustomers);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20';
      case 'inactive': return 'text-gray-400 bg-gray-500/20';
      case 'banned': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  if (loading) {
    return (
      <SuperAdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-white text-xl">Loading customers...</div>
        </div>
      </SuperAdminLayout>
    );
  }

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-white mb-2">Customer Management</h1>
            <p className="text-white/70 font-raleway text-sm sm:text-base">Manage all customers using the TableServe platform</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FaUsers className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">{customers.length}</h3>
            <p className="text-white/70 font-raleway">Total Customers</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <FaShoppingCart className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">{customers.reduce((sum, c) => sum + c.totalOrders, 0)}</h3>
            <p className="text-white/70 font-raleway">Total Orders</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-accent rounded-xl flex items-center justify-center">
                <FaStar className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">
              {(customers.reduce((sum, c) => sum + c.avgRating, 0) / customers.length).toFixed(1)}
            </h3>
            <p className="text-white/70 font-raleway">Avg Rating</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                <FaUser className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">{customers.filter(c => c.status === 'active').length}</h3>
            <p className="text-white/70 font-raleway">Active Users</p>
          </motion.div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50" />
              <input
                type="text"
                placeholder="Search customers by name, phone, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/50 focus:outline-none focus:border-accent"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="banned">Banned</option>
            </select>
            <button className="bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg px-4 py-2 text-white flex items-center space-x-2">
              <FaDownload />
              <span>Export</span>
            </button>
          </div>
        </div>

        {/* Customers Table */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Customer</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Contact</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Activity</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Spending</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Status</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredCustomers.map((customer) => (
                  <motion.tr
                    key={customer.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="border-t border-white/10 hover:bg-white/5 transition-colors"
                  >
                    <td className="p-4">
                      <div>
                        <h4 className="text-white font-raleway font-medium">{customer.name}</h4>
                        <p className="text-white/60 font-raleway text-sm">Joined {customer.joinDate}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-white font-raleway">{customer.phone}</p>
                        <p className="text-white/60 font-raleway text-sm">{customer.email}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-white font-raleway">{customer.totalOrders} orders</p>
                        <p className="text-white/60 font-raleway text-sm">Last: {customer.lastOrder}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-white font-raleway">${customer.totalSpent.toFixed(2)}</p>
                        <div className="flex items-center space-x-1">
                          <FaStar className="text-yellow-400 text-sm" />
                          <span className="text-white/60 font-raleway text-sm">{customer.avgRating}</span>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getStatusColor(customer.status)}`}>
                        {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-green-400 hover:text-green-300 hover:bg-green-500/20 rounded-lg transition-colors">
                          <FaEye />
                        </button>
                        <button className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-colors">
                          <FaBan />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </SuperAdminLayout>
  );
};

export default CustomerManagement;
