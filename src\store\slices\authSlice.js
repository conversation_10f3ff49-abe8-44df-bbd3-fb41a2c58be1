import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import AuthService from '../../services/AuthService';

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async ({ username, password, role }, { rejectWithValue }) => {
    try {
      let result;

      if (role) {
        // Authenticate with specific role
        result = AuthService.authenticateUser({ username, password }, role);
      } else {
        // Try to authenticate with any role
        result = AuthService.authenticateAnyRole(username, password);
      }

      return result;
    } catch (error) {
      return rejectWithValue(error.message || 'Authentication failed');
    }
  }
);

export const logoutUser = createAsyncThunk('auth/logoutUser', async () => {
  AuthService.logout();
  return null;
});

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: AuthService.getCurrentUser(),
    token: localStorage.getItem('auth_token'),
    isAuthenticated: AuthService.isAuthenticated(),
    loading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCredentials: (state, action) => {
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isAuthenticated = true;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
      });
  },
});

export const { clearError, setCredentials } = authSlice.actions;
export default authSlice.reducer;
