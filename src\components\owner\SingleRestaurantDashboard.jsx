import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import DataService from '../../services/DataService';
import AuthService from '../../services/AuthService';
import {
  FaShoppingCart,
  FaUtensils,
  FaTable,
  FaArrowUp,
  FaArrowDown,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaEye,
  FaChartLine,
  FaUsers,
  FaStar,
  FaRupeeSign
} from 'react-icons/fa';
import SingleRestaurantLayout from './SingleRestaurantLayout';
import OrderProcessingService from '../../services/OrderProcessingService';

const SingleRestaurantDashboard = () => {
  const { restaurantId } = useParams(); // Get restaurant ID from URL
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState('today');
  const [loading, setLoading] = useState(true);
  const [liveOrders, setLiveOrders] = useState([]);
  const [refreshInterval, setRefreshInterval] = useState(null);
  const [dashboardData, setDashboardData] = useState(null);

  // Authentication check - run only once when component mounts
  useEffect(() => {
    console.log('Dashboard auth check:', { user, isAuthenticated, restaurantId });

    if (!isAuthenticated || !user) {
      console.log('Not authenticated, redirecting to login');
      navigate('/tableserve/login', { replace: true });
      return;
    }

    if (user.role !== 'restaurant_owner') {
      console.log('Wrong role, redirecting to login');
      navigate('/tableserve/login', { replace: true });
      return;
    }

    // Check if user has access to this restaurant (handle type coercion)
    if (user.restaurantId != restaurantId) {
      console.log('Access denied to restaurant:', {
        userRestaurantId: user.restaurantId,
        userRestaurantIdType: typeof user.restaurantId,
        requestedRestaurantId: restaurantId,
        requestedRestaurantIdType: typeof restaurantId,
        strictEqual: user.restaurantId === restaurantId,
        looseEqual: user.restaurantId == restaurantId
      });
      navigate('/tableserve/login', { replace: true });
      return;
    }

    console.log('Authentication successful for restaurant:', restaurantId);
  }, []); // Empty dependency array - run only once

  useEffect(() => {
    const loadDashboardData = () => {
      setLoading(true);
      try {
        // Use restaurant ID from URL params, fallback to user data
        const targetRestaurantId = restaurantId || (user && user.restaurantId);

        if (targetRestaurantId) {
          const stats = DataService.getRestaurantOwnerStats(targetRestaurantId);

          // Transform stats to match the expected dashboard data structure
          const transformedData = {
            today: {
              totalOrders: stats.todayOrders,
              totalRevenue: parseFloat(stats.todayRevenue.replace(/[₹,]/g, '')) || 0,
              avgOrderValue: parseFloat(stats.averageOrderValue.replace(/[₹,]/g, '')) || 0,
              activeMenuItems: stats.activeMenuItems,
              totalTables: stats.totalTables,
              occupiedTables: Math.min(stats.totalTables, Math.floor(stats.totalTables * 0.7)),
              pendingOrders: Math.floor(stats.todayOrders * 0.1),
              preparingOrders: Math.floor(stats.todayOrders * 0.2),
              readyOrders: Math.floor(stats.todayOrders * 0.1),
              completedOrders: Math.floor(stats.todayOrders * 0.6),
              topItems: stats.popularItems.length > 0 ? stats.popularItems : [
                { name: 'No items yet', orders: 0, revenue: 0 }
              ],
              recentOrders: stats.recentOrders.length > 0 ? stats.recentOrders : [],
              customerFeedback: {
                averageRating: stats.customerSatisfaction || 0,
                totalReviews: 0,
                recentFeedback: []
              }
            }
          };

          setDashboardData(transformedData);
        }
      } catch (error) {
        console.error('Error loading restaurant dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  useEffect(() => {
    // Load live orders from OrderProcessingService
    const loadLiveOrders = () => {
      if (user?.restaurantId) {
        const orders = OrderProcessingService.getLiveOrdersForUser('restaurant', user.restaurantId);
        setLiveOrders(orders);
      }
    };

    // Initial load
    loadLiveOrders();
    setLoading(false);

    // Set up real-time refresh every 5 seconds
    const interval = setInterval(loadLiveOrders, 5000);
    setRefreshInterval(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [user?.restaurantId]);

  const updateOrderStatus = (orderId, newStatus) => {
    if (user?.restaurantId) {
      OrderProcessingService.updateOrderStatus(
        orderId,
        newStatus,
        user.name,
        'restaurant',
        user.restaurantId
      );

      // Refresh orders immediately
      const orders = OrderProcessingService.getLiveOrdersForUser('restaurant', user.restaurantId);
      setLiveOrders(orders);
    }
  };

  const data = dashboardData ? dashboardData[timeRange] : null;

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-500/20';
      case 'preparing': return 'text-blue-400 bg-blue-500/20';
      case 'ready': return 'text-green-400 bg-green-500/20';
      case 'completed': return 'text-gray-400 bg-gray-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  if (loading || !data) {
    return (
      <SingleRestaurantLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-primary font-raleway">Loading dashboard...</p>
          </div>
        </div>
      </SingleRestaurantLayout>
    );
  }

  return (
    <SingleRestaurantLayout>
      <div className="space-y-4 sm:space-y-6 ">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2">
              Welcome back, {user?.name}!
            </h1>
            <p className="text-secondary font-raleway text-sm sm:text-base">
              Here's what's happening at {user?.restaurantName} today
            </p>
          </div>

        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="admin-card rounded-2xl p-4 sm:p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-accent rounded-xl flex items-center justify-center">
                <FaShoppingCart className="text-inverse text-xl" />
              </div>

            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">{data.totalOrders}</h3>
            <p className="text-primary font-raleway font-medium mb-1">Total Orders</p>
            <p className="text-secondary font-raleway text-sm">vs yesterday</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="admin-card rounded-2xl p-4 sm:p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <FaRupeeSign className="text-inverse text-xl" />
              </div>

            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">₹{data.totalRevenue}</h3>
            <p className="text-primary font-raleway font-medium mb-1">Revenue</p>
            <p className="text-secondary font-raleway text-sm">vs yesterday</p>
          </motion.div>




        </div>

        {/* Order Status Overview */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <div className="admin-card rounded-2xl p-4 text-center">
            <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center mx-auto mb-2">
              <FaClock className="text-theme-text-inverse text-sm" />
            </div>
            <p className="text-2xl font-fredoka text-yellow-400">{data.pendingOrders}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Pending</p>
          </div>
          <div className="admin-card rounded-2xl p-4 text-center">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2">
              <FaShoppingCart className="text-theme-text-inverse text-sm" />
            </div>
            <p className="text-2xl font-fredoka text-blue-400">{data.preparingOrders}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Preparing</p>
          </div>
          <div className="admin-card rounded-2xl p-4 text-center">
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-2">
              <FaCheckCircle className="text-theme-text-inverse text-sm" />
            </div>
            <p className="text-2xl font-fredoka text-green-400">{data.readyOrders}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Ready</p>
          </div>
          <div className="admin-card rounded-2xl p-4 text-center">
            <div className="w-8 h-8 bg-gray-500 rounded-lg flex items-center justify-center mx-auto mb-2">
              <FaCheckCircle className="text-theme-text-inverse text-sm" />
            </div>
            <p className="text-2xl font-fredoka text-gray-400">{data.completedOrders}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Completed</p>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1  lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Recent Orders */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-2 admin-card backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg sm:text-xl font-fredoka text-primary">Recent Orders</h2>
              <button className="text-accent hover:text-accent/80 font-raleway text-sm font-semibold">
                View All
              </button>
            </div>
            <div className="space-y-4">
              {data.recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="text-accent font-raleway font-medium">{order.id}</span>
                      <span className="text-primary font-raleway text-sm">Table {order.table}</span>
                      <div className={`px-2 py-1 rounded-full text-xs font-raleway ${getStatusColor(order.status)}`}>
                        {order.status}
                      </div>
                    </div>
                    <p className="text-primary font-raleway text-sm">
                      {order.items.join(', ')}
                    </p>
                    <p className="text-secondary font-raleway text-xs mt-1">{order.time}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-secondary font-raleway font-semibold">${order.total}</p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Top Menu Items */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="admin-card backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <h2 className="text-lg sm:text-xl font-fredoka text-primary mb-6">Top Menu Items</h2>
            <div className="space-y-4">
              {data.topItems.map((item, index) => (
                <div key={item.name} className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center text-white font-fredoka text-sm">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-primary font-raleway font-medium">{item.name}</h4>
                    <p className="text-secondary font-raleway text-sm">{item.orders} orders</p>
                  </div>
                  <div className="text-right">
                    <p className="text-accent font-raleway font-semibold">${item.revenue}</p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Customer Feedback */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="admin-card backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
        >
          <h2 className="text-lg sm:text-xl font-fredoka text-primary mb-6">Recent Customer Feedback</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {data.customerFeedback.recentFeedback.map((feedback, index) => (
              <div key={index} className="p-4 bg-secondary rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <FaStar
                        key={i}
                        className={`text-sm ${i < feedback.rating ? 'text-yellow-400' : 'text-gray-600'
                          }`}
                      />
                    ))}
                  </div>
                  <span className="text-primary font-raleway text-sm">{feedback.time}</span>
                </div>
                <p className="text-secondary font-raleway text-sm">{feedback.comment}</p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </SingleRestaurantLayout>
  );
};

export default SingleRestaurantDashboard;
