import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaUtensils,
  FaSearch,
  FaSave,
  FaTimes,
  FaLeaf,
  FaFire
} from 'react-icons/fa';
import SingleRestaurantLayout from '../../components/owner/SingleRestaurantLayout';
import ImageUpload from '../../components/common/ImageUpload';
import { getAvailableCategories } from '../../utils/categoryUtils';

const MenuManagement = () => {
  const { restaurantId } = useParams();
  const { user } = useSelector((state) => state.auth);
  const [menuItems, setMenuItems] = useState([]);
  const [availableCategories, setAvailableCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    isVeg: false,
    isSpicy: false,
    image: null,
    available: true
  });

  // Mock menu data
  const mockMenuItems = [
    {
      id: 1,
      name: 'Margherita Pizza',
      description: 'Classic pizza with tomato sauce, mozzarella, and fresh basil',
      price: 12.99,
      category: 'Pizza',
      isVeg: true,
      isSpicy: false,
      available: true,
      image: null,
      sales: 45
    },
    {
      id: 2,
      name: 'Chicken Tikka Masala',
      description: 'Tender chicken in creamy tomato-based curry sauce',
      price: 16.99,
      category: 'Main Course',
      isVeg: false,
      isSpicy: true,
      available: true,
      image: null,
      sales: 32
    },
    {
      id: 3,
      name: 'Caesar Salad',
      description: 'Fresh romaine lettuce with parmesan cheese and croutons',
      price: 9.99,
      category: 'Salads',
      isVeg: true,
      isSpicy: false,
      available: false,
      image: null,
      sales: 18
    },
    {
      id: 4,
      name: 'Beef Burger',
      description: 'Juicy beef patty with lettuce, tomato, and special sauce',
      price: 14.99,
      category: 'Burgers',
      isVeg: false,
      isSpicy: false,
      available: true,
      image: null,
      sales: 28
    }
  ];

  useEffect(() => {
    loadCategories();
    // Simulate loading
    setTimeout(() => {
      setMenuItems(mockMenuItems);
      setLoading(false);
    }, 1000);
  }, [restaurantId]);

  const loadCategories = () => {
    try {
      // Get categories from both restaurant and zone (if applicable)
      const categories = getAvailableCategories(restaurantId, user?.zoneId);
      setAvailableCategories(categories);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (editingItem) {
      // Update existing item
      setMenuItems(prev => prev.map(item =>
        item.id === editingItem.id
          ? { ...formData, id: editingItem.id, sales: editingItem.sales }
          : item
      ));
    } else {
      // Add new item
      const newItem = {
        ...formData,
        id: Date.now(),
        sales: 0,
        price: parseFloat(formData.price)
      };
      setMenuItems(prev => [...prev, newItem]);
    }
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      category: '',
      isVeg: false,
      isSpicy: false,
      image: null,
      available: true
    });
    setEditingItem(null);
    setShowModal(false);
  };

  const handleEdit = (item) => {
    setFormData(item);
    setEditingItem(item);
    setShowModal(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      setMenuItems(prev => prev.filter(item => item.id !== id));
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const filteredItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const categories = ['all', ...availableCategories.map(cat => cat.name)];

  if (loading) {
    return (
      <SingleRestaurantLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 spinner-theme rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-primary font-raleway">Loading menu items...</p>
          </div>
        </div>
      </SingleRestaurantLayout>
    );
  }

  return (
    <SingleRestaurantLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2">Menu Management</h1>
            <p className="text-secondary font-raleway text-sm sm:text-base">Manage your restaurant's menu items</p>
          </div>
          <button
            onClick={() => setShowModal(true)}
            className="btn-primary px-6 py-3 rounded-lg font-raleway font-semibold flex items-center space-x-2"
          >
            <FaPlus />
            <span>Add Item</span>
          </button>
        </div>

        {/* Filters */}
        <div className="admin-card rounded-2xl p-4 sm:p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-text-tertiary" />
              <input
                type="text"
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full input-theme rounded-lg pl-10 pr-4 py-2 focus:outline-none"
              />
            </div>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="input-theme rounded-lg px-4 py-2 focus:outline-none"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Menu Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="admin-card rounded-2xl p-6 hover:border-theme-accent-primary/30 transition-all duration-300"
            >
              {/* Item Image */}
              <div className="w-full h-32 bg-theme-bg-secondary rounded-lg mb-4 flex items-center justify-center">
                {item.image ? (
                  <img src={item.image} alt={item.name} className="w-full h-full object-cover rounded-lg" />
                ) : (
                  <FaUtensils className="text-3xl text-theme-text-tertiary" />
                )}
              </div>

              {/* Item Info */}
              <div className="space-y-3">
                <div className="flex justify-between items-start">
                  <h3 className="text-lg font-fredoka text-theme-text-primary">{item.name}</h3>
                  <div className="flex space-x-1">
                    {item.isVeg && <FaLeaf className="text-green-500" />}
                    {item.isSpicy && <FaFire className="text-red-500" />}
                  </div>
                </div>

                <p className="text-theme-text-secondary font-raleway text-sm line-clamp-2">{item.description}</p>

                <div className="flex justify-between items-center">
                  <span className="text-xl font-fredoka text-theme-accent-primary">₹{item.price}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-raleway ${item.available
                    ? 'bg-green-500/20 text-green-500'
                    : 'bg-red-500/20 text-red-500'
                    }`}>
                    {item.available ? 'Available' : 'Unavailable'}
                  </span>
                </div>

                <div className="text-theme-text-tertiary font-raleway text-xs">
                  Sales: {item.sales} orders
                </div>
              </div>

              {/* Actions */}
              <div className="mt-4 pt-4 border-t border-theme-border-primary flex space-x-2">
                <button
                  onClick={() => handleEdit(item)}
                  className="flex-1 btn-secondary py-2 rounded-lg font-raleway flex items-center justify-center space-x-1"
                >
                  <FaEdit />
                  <span>Edit</span>
                </button>
                <button
                  onClick={() => handleDelete(item.id)}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-theme-text-inverse py-2 rounded-lg font-raleway flex items-center justify-center space-x-1"
                >
                  <FaTrash />
                  <span>Delete</span>
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <FaUtensils className="text-6xl text-theme-text-tertiary mx-auto mb-4" />
            <h3 className="text-xl font-fredoka text-theme-text-primary mb-2">No Menu Items Found</h3>
            <p className="text-theme-text-secondary font-raleway">No items match your current filters.</p>
          </div>
        )}

        {/* Add/Edit Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="admin-card rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-fredoka text-theme-text-primary">
                  {editingItem ? 'Edit Menu Item' : 'Add Menu Item'}
                </h2>
                <button
                  onClick={resetForm}
                  className="text-theme-text-tertiary hover:text-theme-text-primary"
                >
                  <FaTimes />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-primary font-raleway font-medium mb-2">Name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">Description</label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    required
                    className="w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none h-20 resize-none"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-theme-text-primary font-raleway font-medium mb-2">Price</label>
                    <input
                      type="number"
                      name="price"
                      value={formData.price}
                      onChange={handleChange}
                      required
                      step="0.01"
                      min="0"
                      className="w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none"
                    />
                  </div>
                  <div>
                    <label className="block text-primary font-raleway font-medium mb-2">Category</label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      required
                      className="w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none"
                    >
                      <option value="">Select a category</option>
                      {availableCategories.map(category => (
                        <option key={category.id} value={category.name}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {availableCategories.length === 0 && (
                      <p className="text-tertiary font-raleway text-sm mt-1">
                        No categories available. Create categories first in Menu → Categories.
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-primary font-raleway font-medium mb-2">Item Image</label>
                  <ImageUpload
                    currentImage={formData.image}
                    onImageChange={(imageUrl) => setFormData({ ...formData, image: imageUrl })}
                    label="Upload item image"
                    size="large"
                    shape="rounded"
                  />
                </div>

                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      name="isVeg"
                      checked={formData.isVeg}
                      onChange={handleChange}
                      className="rounded"
                    />
                    <span className="text-theme-text-primary font-raleway">Vegetarian</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      name="isSpicy"
                      checked={formData.isSpicy}
                      onChange={handleChange}
                      className="rounded"
                    />
                    <span className="text-theme-text-primary font-raleway">Spicy</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      name="available"
                      checked={formData.available}
                      onChange={handleChange}
                      className="rounded"
                    />
                    <span className="text-theme-text-primary font-raleway">Available</span>
                  </label>
                </div>

                <div className="flex space-x-4 pt-4">
                  <button
                    type="submit"
                    className="flex-1 btn-primary py-2 rounded-lg font-raleway flex items-center justify-center space-x-2"
                  >
                    <FaSave />
                    <span>{editingItem ? 'Update' : 'Add'} Item</span>
                  </button>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="flex-1 btn-secondary py-2 rounded-lg font-raleway flex items-center justify-center space-x-2"
                  >
                    <FaTimes />
                    <span>Cancel</span>
                  </button>
                </div>
              </form>
            </motion.div>
          </div>
        )}
      </div>
    </SingleRestaurantLayout>
  );
};

export default MenuManagement;
