// Utility functions for managing categories across different admin levels

/**
 * Get all available categories for a restaurant
 * Combines restaurant-specific categories with zone categories (if applicable)
 */
export const getAvailableCategories = (restaurantId, zoneId = null) => {
  const categories = [];

  try {
    // Get restaurant-specific categories
    const restaurantCategories = localStorage.getItem(`categories_${restaurantId}`);
    if (restaurantCategories) {
      const parsed = JSON.parse(restaurantCategories);
      categories.push(...parsed.filter(cat => cat.isActive));
    }

    // Get zone categories if zone ID is provided
    if (zoneId) {
      const zoneCategories = localStorage.getItem(`zone_categories_${zoneId}`);
      if (zoneCategories) {
        const parsed = JSON.parse(zoneCategories);
        const availableZoneCategories = parsed.filter(cat => 
          cat.isActive && cat.availableForShops === 'all'
        );
        categories.push(...availableZoneCategories.map(cat => ({
          ...cat,
          isZoneCategory: true,
          source: 'zone'
        })));
      }
    }

    // Remove duplicates based on name
    const uniqueCategories = categories.filter((category, index, self) =>
      index === self.findIndex(c => c.name === category.name)
    );

    return uniqueCategories.sort((a, b) => a.sortOrder - b.sortOrder);
  } catch (error) {
    console.error('Error loading categories:', error);
    return [];
  }
};

/**
 * Get all available modifiers for a restaurant
 * Combines restaurant-specific modifiers with zone modifiers (if applicable)
 */
export const getAvailableModifiers = (restaurantId, zoneId = null) => {
  const modifiers = [];

  try {
    // Get restaurant-specific modifiers
    const restaurantModifiers = localStorage.getItem(`modifiers_${restaurantId}`);
    if (restaurantModifiers) {
      const parsed = JSON.parse(restaurantModifiers);
      modifiers.push(...parsed);
    }

    // Get zone modifiers if zone ID is provided
    if (zoneId) {
      const zoneModifiers = localStorage.getItem(`zone_modifiers_${zoneId}`);
      if (zoneModifiers) {
        const parsed = JSON.parse(zoneModifiers);
        const availableZoneModifiers = parsed.filter(mod => 
          mod.availableForShops === 'all'
        );
        modifiers.push(...availableZoneModifiers.map(mod => ({
          ...mod,
          isZoneModifier: true,
          source: 'zone'
        })));
      }
    }

    // Remove duplicates based on name
    const uniqueModifiers = modifiers.filter((modifier, index, self) =>
      index === self.findIndex(m => m.name === modifier.name)
    );

    return uniqueModifiers;
  } catch (error) {
    console.error('Error loading modifiers:', error);
    return [];
  }
};

/**
 * Save a new category for a restaurant
 */
export const saveRestaurantCategory = (restaurantId, categoryData) => {
  try {
    const existingCategories = localStorage.getItem(`categories_${restaurantId}`);
    const categories = existingCategories ? JSON.parse(existingCategories) : [];
    
    const newCategory = {
      id: Date.now(),
      ...categoryData,
      createdAt: new Date().toISOString(),
      source: 'restaurant'
    };

    categories.push(newCategory);
    localStorage.setItem(`categories_${restaurantId}`, JSON.stringify(categories));
    
    return newCategory;
  } catch (error) {
    console.error('Error saving category:', error);
    throw error;
  }
};

/**
 * Save a new modifier for a restaurant
 */
export const saveRestaurantModifier = (restaurantId, modifierData) => {
  try {
    const existingModifiers = localStorage.getItem(`modifiers_${restaurantId}`);
    const modifiers = existingModifiers ? JSON.parse(existingModifiers) : [];
    
    const newModifier = {
      id: Date.now(),
      ...modifierData,
      createdAt: new Date().toISOString(),
      source: 'restaurant'
    };

    modifiers.push(newModifier);
    localStorage.setItem(`modifiers_${restaurantId}`, JSON.stringify(modifiers));
    
    return newModifier;
  } catch (error) {
    console.error('Error saving modifier:', error);
    throw error;
  }
};

/**
 * Get category usage statistics for zone admin
 */
export const getCategoryUsageStats = (zoneId) => {
  try {
    const zoneCategories = localStorage.getItem(`zone_categories_${zoneId}`);
    if (!zoneCategories) return [];

    const categories = JSON.parse(zoneCategories);
    
    // This would normally query the database for actual usage
    // For now, we'll return mock usage data
    return categories.map(category => ({
      ...category,
      shopsUsing: Math.floor(Math.random() * 20) + 1,
      itemsCreated: Math.floor(Math.random() * 50) + 5,
      lastUsed: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    }));
  } catch (error) {
    console.error('Error getting category usage stats:', error);
    return [];
  }
};

/**
 * Get modifier usage statistics for zone admin
 */
export const getModifierUsageStats = (zoneId) => {
  try {
    const zoneModifiers = localStorage.getItem(`zone_modifiers_${zoneId}`);
    if (!zoneModifiers) return [];

    const modifiers = JSON.parse(zoneModifiers);
    
    // This would normally query the database for actual usage
    // For now, we'll return mock usage data
    return modifiers.map(modifier => ({
      ...modifier,
      shopsUsing: Math.floor(Math.random() * 15) + 1,
      timesUsed: Math.floor(Math.random() * 200) + 10,
      lastUsed: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    }));
  } catch (error) {
    console.error('Error getting modifier usage stats:', error);
    return [];
  }
};

/**
 * Check if a category name already exists
 */
export const categoryNameExists = (restaurantId, categoryName, excludeId = null) => {
  try {
    const categories = getAvailableCategories(restaurantId);
    return categories.some(cat => 
      cat.name.toLowerCase() === categoryName.toLowerCase() && 
      cat.id !== excludeId
    );
  } catch (error) {
    console.error('Error checking category name:', error);
    return false;
  }
};

/**
 * Check if a modifier name already exists
 */
export const modifierNameExists = (restaurantId, modifierName, excludeId = null) => {
  try {
    const modifiers = getAvailableModifiers(restaurantId);
    return modifiers.some(mod => 
      mod.name.toLowerCase() === modifierName.toLowerCase() && 
      mod.id !== excludeId
    );
  } catch (error) {
    console.error('Error checking modifier name:', error);
    return false;
  }
};
