import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import DataService from '../../services/DataService';
import LocalStorageService from '../../services/LocalStorageService';
import {
  FaStore,
  FaUsers,
  FaRupeeSign,
  FaShoppingCart,
  FaArrowUp,
  FaArrowDown,
  FaChartLine,
  FaExclamationTriangle,
  FaCheckCircle,
  FaMapMarkerAlt,
  FaQrcode,
  FaEye
} from 'react-icons/fa';
import SuperAdminLayout from './SuperAdminLayout';

const SuperAdminDashboard = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);

  useEffect(() => {
    const loadDashboardData = () => {
      setLoading(true);
      try {
        const dashboardStats = DataService.getSuperAdminStats();
        setStats(dashboardStats);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Real dashboard data from local storage
  const kpiData = stats ? [
    {
      title: 'Total Restaurants',
      value: stats.totalRestaurants.toString(),
      change: stats.monthlyGrowth > 0 ? `+${stats.monthlyGrowth}%` : '0%',
      trend: stats.monthlyGrowth > 0 ? 'up' : 'neutral',
      icon: FaStore,
      color: 'bg-status-info',
      description: `${stats.activeRestaurants} active restaurants`
    },
    {
      title: 'Food Zones',
      value: stats.totalZones.toString(),
      change: stats.monthlyGrowth > 0 ? `+${Math.round(stats.monthlyGrowth / 4)}` : '0',
      trend: stats.monthlyGrowth > 0 ? 'up' : 'neutral',
      icon: FaMapMarkerAlt,
      color: 'bg-theme-accent-primary',
      description: `${stats.activeZones} active zones`
    },
    {
      title: 'Total Revenue',
      value: stats.totalRevenue,
      change: stats.monthlyGrowth > 0 ? `+${stats.monthlyGrowth}%` : '0%',
      trend: stats.monthlyGrowth > 0 ? 'up' : 'neutral',
      icon: FaRupeeSign,
      color: 'bg-theme-accent-primary',
      description: 'Platform revenue total'
    },
    {
      title: 'Total Orders',
      value: stats.totalOrders.toString(),
      change: stats.monthlyGrowth > 0 ? `+${stats.monthlyGrowth}%` : '0%',
      trend: stats.monthlyGrowth > 0 ? 'up' : 'neutral',
      icon: FaShoppingCart,
      color: 'bg-status-success',
      description: 'Orders processed total'
    },
    {
      title: 'Active Users',
      value: stats.activeUsers.toString(),
      change: '0%',
      trend: 'neutral',
      icon: FaUsers,
      color: 'bg-status-warning',
      description: 'Active restaurant & zone owners'
    },
    {
      title: 'QR Scans Today',
      value: '0',
      change: '0%',
      trend: 'neutral',
      icon: FaQrcode,
      color: 'bg-status-info',
      description: 'QR code scans today'
    }
  ] : [];

  // Generate recent activity from real data
  const recentActivity = stats ? (() => {
    const activities = [];

    if (stats.totalRestaurants > 0) {
      activities.push({
        id: 1,
        type: 'success',
        title: 'Restaurants Active',
        description: `${stats.activeRestaurants} restaurants currently active on platform`,
        time: 'Current status',
        icon: FaCheckCircle
      });
    }

    if (stats.totalZones > 0) {
      activities.push({
        id: 2,
        type: 'info',
        title: 'Food Zones Operating',
        description: `${stats.activeZones} food zones currently operational`,
        time: 'Current status',
        icon: FaChartLine
      });
    }

    if (stats.totalOrders === 0) {
      activities.push({
        id: 3,
        type: 'warning',
        title: 'No Orders Yet',
        description: 'Platform ready for first orders',
        time: 'Waiting for activity',
        icon: FaExclamationTriangle
      });
    }

    if (activities.length === 0) {
      activities.push({
        id: 1,
        type: 'info',
        title: 'Platform Ready',
        description: 'System initialized and ready for restaurants and zones',
        time: 'System status',
        icon: FaCheckCircle
      });
    }

    return activities;
  })() : [];

  // Generate top performers from real data
  const topPerformers = stats ? (() => {
    const performers = [];
    const restaurants = LocalStorageService.getRestaurants();
    const zones = LocalStorageService.getZones();

    // Add restaurants with revenue data
    restaurants.forEach(restaurant => {
      if (restaurant.revenue && restaurant.orders) {
        performers.push({
          name: restaurant.name,
          type: 'Restaurant',
          revenue: restaurant.revenue,
          orders: restaurant.orders
        });
      }
    });

    // Add zones with revenue data
    zones.forEach(zone => {
      if (zone.totalRevenue && zone.totalOrders) {
        performers.push({
          name: zone.name,
          type: 'Zone',
          revenue: zone.totalRevenue,
          orders: zone.totalOrders
        });
      }
    });

    // Sort by revenue (extract number from string like "₹1,234")
    performers.sort((a, b) => {
      const aRevenue = parseInt(a.revenue.replace(/[₹,]/g, '')) || 0;
      const bRevenue = parseInt(b.revenue.replace(/[₹,]/g, '')) || 0;
      return bRevenue - aRevenue;
    });

    return performers.slice(0, 4); // Top 4 performers
  })() : [];


  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'success': return <FaCheckCircle className="status-success" />;
      case 'warning': return <FaExclamationTriangle className="status-warning" />;
      case 'error': return <FaExclamationTriangle className="status-error" />;
      default: return <FaChartLine className="status-info" />;
    }
  };


  if (loading) {
    return (
      <SuperAdminLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 spinner-theme rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-primary font-raleway">Loading dashboard...</p>
          </div>
        </div>
      </SuperAdminLayout>
    );
  }

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-raleway text-theme-text-primary mb-2">Super Admin Dashboard</h1>
            <p className="text-theme-text-secondary font-raleway text-sm sm:text-base">Complete platform overview and management</p>
          </div>

        </div>


        {/* KPI Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
        >
          {kpiData.map((kpi, index) => (
            <motion.div
              key={kpi.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="admin-card rounded-2xl p-6 hover:border-theme-accent-primary/30 transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 ${kpi.color} rounded-xl flex items-center justify-center`}>
                  <kpi.icon className="text-theme-text-inverse text-xl" />
                </div>

              </div>
              <h3 className="text-2xl font-fredoka text-theme-text-primary mb-1">{kpi.value}</h3>
              <p className="text-theme-text-primary font-raleway font-medium mb-1">{kpi.title}</p>
              <p className="text-theme-text-tertiary font-raleway text-sm">{kpi.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-2 admin-card rounded-2xl p-4 sm:p-6"
          >
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-2 sm:space-y-0">
              <h2 className="text-lg sm:text-xl font-fredoka text-theme-text-primary">Recent Activity</h2>
              <button className="text-theme-accent-primary hover:text-theme-accent-hover font-raleway text-sm self-start sm:self-auto">
                View All
              </button>
            </div>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-4 p-4 rounded-lg hover:bg-theme-bg-hover transition-colors">
                  <div className="flex-shrink-0 mt-1">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-theme-text-primary font-raleway font-medium">{activity.title}</h4>
                    <p className="text-theme-text-secondary font-raleway text-sm mt-1">{activity.description}</p>
                    <p className="text-theme-text-tertiary font-raleway text-xs mt-2">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Top Performers */}

        </div>
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminDashboard;
