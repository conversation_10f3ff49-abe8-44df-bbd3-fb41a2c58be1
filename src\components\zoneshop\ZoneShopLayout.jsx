import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, NavLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaTachometerAlt,
  FaUser,
  FaUtensils,
  FaList,
  FaShoppingCart,
  FaHistory,
  FaChartBar,
  FaCog,
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaStore
} from 'react-icons/fa';
import { logoutUser } from '../../store/slices/authSlice';
import ThemeToggle from '../common/ThemeToggle';
import { initializeTheme, selectTheme } from '../../store/slices/themeSlice';

const ZoneShopLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const currentTheme = useSelector(selectTheme);

  useEffect(() => {
    // Initialize theme
    dispatch(initializeTheme());
  }, [dispatch]);

  useEffect(() => {
    // Check authentication and role
    if (!isAuthenticated || user?.role !== 'zone-shop') {
      navigate('/tableserve/login');
      return;
    }
    setLoading(false);
  }, [isAuthenticated, user, navigate]);

  const handleLogout = () => {
    dispatch(logoutUser());
    navigate('/tableserve/login');
  };

  const menuItems = [
    { path: '/zone-shop/dashboard', icon: FaTachometerAlt, label: 'Dashboard' },
    { path: '/zone-shop/profile', icon: FaUser, label: 'Shop Profile' },
    { path: '/zone-shop/menu/items', icon: FaUtensils, label: 'Menu Items' },
    { path: '/zone-shop/menu/categories', icon: FaList, label: 'Categories' },
    { path: '/zone-shop/menu/modifiers', icon: FaCog, label: 'Modifiers' },
    { path: '/zone-shop/orders/live', icon: FaShoppingCart, label: 'Live Orders' },
    { path: '/zone-shop/orders/history', icon: FaHistory, label: 'Order History' },
    { path: '/zone-shop/analytics', icon: FaChartBar, label: 'Analytics' },
  ];

  if (loading) {
    return (
      <div className="min-h-screen admin-layout flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 spinner-theme rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-theme-text-primary font-raleway">Loading Zone Shop Panel...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen admin-layout theme-transition">
      {/* Top Navigation */}
      <nav className="admin-navbar px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden text-theme-text-primary p-2 rounded-lg hover:bg-theme-bg-hover"
            >
              {sidebarOpen ? <FaTimes /> : <FaBars />}
            </button>
            <div className="flex items-center space-x-3">
              <FaStore className="text-theme-accent-primary text-2xl" />
              <div>
                <h1 className="text-xl font-fredoka text-theme-text-primary">{user?.shopName || 'Zone Shop'}</h1>
                <p className="text-theme-text-tertiary text-sm font-raleway">{user?.zoneName || 'Food Zone'}</p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle variant="icon-only" showLabel={false} />

            <div className="hidden md:block text-right">
              <p className="text-theme-text-primary font-raleway text-sm">{user?.name}</p>
              <p className="text-theme-text-tertiary text-xs font-raleway">Zone Shop Owner</p>
            </div>
            <button
              onClick={handleLogout}
              className="text-theme-text-secondary hover:text-theme-text-primary p-2 rounded-lg hover:bg-theme-bg-hover transition-colors"
              title="Logout"
            >
              <FaSignOutAlt />
            </button>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'
          } lg:translate-x-0 fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white/5 backdrop-blur-lg border-r border-white/20 transition-transform duration-300 ease-in-out`}>
          <div className="p-6 pt-20 lg:pt-6 h-full flex flex-col">
            <nav className="flex-1 overflow-y-auto scrollbar-theme space-y-2" style={{ maxHeight: 'calc(100vh - 12rem)' }}>
              {menuItems.map((item) => (
                <NavLink
                  key={item.path}
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${isActive
                      ? 'bg-accent text-white'
                      : 'text-white/80 hover:text-white hover:bg-white/10'
                    }`
                  }
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon className="w-5 h-5" />
                  <span className="font-raleway">{item.label}</span>
                </NavLink>
              ))}
            </nav>
          </div>
        </aside>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <div className="flex-1 lg:ml-0">
          <main className="p-6">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};

export default ZoneShopLayout;
