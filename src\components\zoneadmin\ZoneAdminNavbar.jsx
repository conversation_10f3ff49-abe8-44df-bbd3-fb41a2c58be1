import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaBars,
  FaTimes,
  FaBell,
  FaUser,
  FaSignOutAlt,
  FaCog,
  FaSearch,
  FaShoppingCart,
  FaExclamationTriangle,
  FaCheckCircle,
  FaStore,
  FaMapMarkerAlt
} from 'react-icons/fa';
import { logoutUser } from '../../store/slices/authSlice';
import ThemeToggle from '../common/ThemeToggle';
import { selectTheme } from '../../store/slices/themeSlice';

const ZoneAdminNavbar = ({ sidebarOpen, setSidebarOpen, isMobile }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { zoneId } = useParams();
  const { user } = useSelector((state) => state.auth);
  const currentTheme = useSelector(selectTheme);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock notifications for zone admin
  const notifications = [
    {
      id: 1,
      type: 'order',
      title: 'New Shop Order',
      message: 'Pizza Corner - Table 3 - ₹450',
      time: '3 minutes ago',
      read: false,
      icon: FaShoppingCart,
      color: 'text-green-400'
    },
    {
      id: 2,
      type: 'alert',
      title: 'Shop Status Update',
      message: 'Burger Barn is now offline',
      time: '20 minutes ago',
      read: false,
      icon: FaExclamationTriangle,
      color: 'text-yellow-400'
    },
    {
      id: 3,
      type: 'success',
      title: 'New Vendor Added',
      message: 'Sushi Zen successfully onboarded',
      time: '2 hours ago',
      read: true,
      icon: FaCheckCircle,
      color: 'text-blue-400'
    }
  ];

  const unreadCount = notifications.filter(n => !n.read).length;

  const handleLogout = () => {
    dispatch(logoutUser());
    navigate('/tableserve/login');
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Implement search functionality
      console.log('Searching for:', searchQuery);
    }
  };

  const handleProfileClick = () => {
    navigate(`/tableserve/zone/${zoneId}/profile`);
    setShowProfile(false);
  };

  return (
    <motion.nav
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="fixed top-0 right-0 left-0 admin-navbar z-50"
    >
      <div className="flex items-center justify-between px-3 sm:px-6 py-4">
        {/* Left Section */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-theme-text-primary hover:text-theme-accent-primary transition-colors p-2 rounded-lg hover:bg-theme-bg-hover"
            title={sidebarOpen ? 'Close Sidebar' : 'Open Sidebar'}
          >
            {sidebarOpen ? (
              <FaTimes className="w-5 h-5" />
            ) : (
              <FaBars className="w-5 h-5" />
            )}
          </button>

          {/* Zone Info */}
          <div className="flex items-center space-x-3">
            <FaMapMarkerAlt className="text-theme-accent-primary text-xl" />
            <div className="hidden sm:block">
              <h1 className="text-lg font-fredoka text-theme-text-primary">{user?.zoneName || 'Food Zone'}</h1>
              <p className="text-theme-text-tertiary text-xs font-raleway">Zone Administration</p>
            </div>
          </div>

          {/* Search - Hidden on mobile */}
          {!isMobile && (
            <form onSubmit={handleSearch} className="relative ml-4">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-text-tertiary text-sm" />
              <input
                type="text"
                placeholder="Search vendors, shops..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-theme rounded-lg pl-10 pr-4 py-2 w-64 text-sm focus:outline-none"
              />
            </form>
          )}
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative text-theme-text-secondary hover:text-theme-text-primary transition-colors p-2 rounded-lg hover:bg-theme-bg-hover"
            >
              <FaBell className="w-5 h-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {unreadCount}
                </span>
              )}
            </button>

            {/* Notifications Dropdown */}
            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute right-0 mt-2 w-80 admin-dropdown rounded-xl shadow-lg z-50"
                >
                  <div className="p-4 border-b border-theme-border-primary">
                    <h3 className="font-fredoka text-theme-text-primary">Notifications</h3>
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-theme-border-primary hover:bg-theme-bg-hover transition-colors ${!notification.read ? 'bg-theme-bg-secondary' : ''
                          }`}
                      >
                        <div className="flex items-start space-x-3">
                          <notification.icon className={`${notification.color} text-lg mt-1`} />
                          <div className="flex-1">
                            <h4 className="font-raleway font-medium text-theme-text-primary text-sm">
                              {notification.title}
                            </h4>
                            <p className="text-theme-text-secondary text-xs mt-1">
                              {notification.message}
                            </p>
                            <p className="text-theme-text-tertiary text-xs mt-1">
                              {notification.time}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Theme Toggle */}
          <ThemeToggle variant="icon-only" showLabel={false} />

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowProfile(!showProfile)}
              className="flex items-center space-x-2 text-theme-text-secondary hover:text-theme-text-primary transition-colors p-2 rounded-lg hover:bg-theme-bg-hover"
            >
              <FaUser className="w-5 h-5" />
              <span className="hidden md:inline font-raleway text-sm">{user?.name}</span>
            </button>

            {/* Profile Dropdown */}
            <AnimatePresence>
              {showProfile && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute right-0 mt-2 w-48 admin-dropdown rounded-xl shadow-lg z-50"
                >
                  <div className="p-4 border-b border-theme-border-primary">
                    <p className="font-fredoka text-theme-text-primary">{user?.name}</p>
                    <p className="text-theme-text-tertiary text-xs font-raleway">Zone Administrator</p>
                  </div>
                  <div className="p-2">
                    <button
                      onClick={handleProfileClick}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-theme-text-secondary hover:text-theme-text-primary hover:bg-theme-bg-hover rounded-lg transition-colors"
                    >
                      <FaUser className="w-4 h-4" />
                      <span className="font-raleway text-sm">Zone Profile</span>
                    </button>
                    <button
                      onClick={() => navigate(`/tableserve/zone/${zoneId}/settings`)}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-theme-text-secondary hover:text-theme-text-primary hover:bg-theme-bg-hover rounded-lg transition-colors"
                    >
                      <FaCog className="w-4 h-4" />
                      <span className="font-raleway text-sm">Settings</span>
                    </button>
                    <hr className="my-2 border-theme-border-primary" />
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-red-400 hover:text-red-300 hover:bg-theme-bg-hover rounded-lg transition-colors"
                    >
                      <FaSignOutAlt className="w-4 h-4" />
                      <span className="font-raleway text-sm">Logout</span>
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Click outside to close dropdowns */}
      {(showNotifications || showProfile) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowNotifications(false);
            setShowProfile(false);
          }}
        />
      )}
    </motion.nav>
  );
};

export default ZoneAdminNavbar;
