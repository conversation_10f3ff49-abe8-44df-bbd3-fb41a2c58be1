import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FaFileExport, 
  FaDownload,
  FaCalendarAlt,
  FaChartBar,
  FaDollarSign,
  FaUsers,
  FaStore,
  FaShoppingCart,
  FaTable,
  FaFilter,
  FaEye,
  FaSpinner
} from 'react-icons/fa';
import SuperAdminLayout from '../SuperAdminLayout';

const Reports = () => {
  const [selectedReport, setSelectedReport] = useState('revenue');
  const [dateRange, setDateRange] = useState('last30days');
  const [format, setFormat] = useState('pdf');
  const [generating, setGenerating] = useState(false);

  const reportTypes = [
    {
      id: 'revenue',
      title: 'Revenue Report',
      description: 'Comprehensive revenue analysis across all restaurants and zones',
      icon: FaDollarSign,
      color: 'bg-status-success'
    },
    {
      id: 'orders',
      title: 'Orders Report',
      description: 'Detailed order statistics and trends',
      icon: FaShoppingCart,
      color: 'bg-status-info'
    },
    {
      id: 'customers',
      title: 'Customer Report',
      description: 'Customer behavior and engagement metrics',
      icon: FaUsers,
      color: 'bg-status-warning'
    },
    {
      id: 'restaurants',
      title: 'Restaurant Performance',
      description: 'Individual restaurant performance metrics',
      icon: FaStore,
      color: 'bg-theme-accent-primary'
    },
    {
      id: 'tables',
      title: 'Table Utilization',
      description: 'Table occupancy and turnover analysis',
      icon: FaTable,
      color: 'bg-status-error'
    },
    {
      id: 'analytics',
      title: 'Platform Analytics',
      description: 'Overall platform usage and performance',
      icon: FaChartBar,
      color: 'bg-status-info'
    }
  ];

  const dateRanges = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last7days', label: 'Last 7 Days' },
    { value: 'last30days', label: 'Last 30 Days' },
    { value: 'last90days', label: 'Last 90 Days' },
    { value: 'thismonth', label: 'This Month' },
    { value: 'lastmonth', label: 'Last Month' },
    { value: 'thisyear', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const formats = [
    { value: 'pdf', label: 'PDF', icon: FaFileExport },
    { value: 'excel', label: 'Excel', icon: FaDownload },
    { value: 'csv', label: 'CSV', icon: FaDownload }
  ];

  const handleGenerateReport = async () => {
    setGenerating(true);
    
    // Simulate report generation
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // In a real app, this would call the backend API
    const reportData = {
      type: selectedReport,
      dateRange,
      format,
      generatedAt: new Date().toISOString()
    };
    
    console.log('Generating report:', reportData);
    
    // Simulate file download
    const filename = `${selectedReport}-report-${dateRange}.${format}`;
    alert(`Report "${filename}" has been generated and downloaded!`);
    
    setGenerating(false);
  };

  const getReportPreview = () => {
    const report = reportTypes.find(r => r.id === selectedReport);
    return {
      title: report?.title || 'Report',
      estimatedSize: '2.5 MB',
      estimatedTime: '30 seconds',
      dataPoints: '15,000+',
      charts: '8',
      tables: '12'
    };
  };

  const preview = getReportPreview();

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-primary mb-2">Reports</h1>
            <p className="text-theme-text-secondary font-raleway text-sm sm:text-base">Generate comprehensive platform reports</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Report Selection */}
          <div className="lg:col-span-2 space-y-6">
            {/* Report Types */}
            <div className="admin-card rounded-2xl p-6">
              <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Select Report Type</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {reportTypes.map((report) => (
                  <motion.button
                    key={report.id}
                    onClick={() => setSelectedReport(report.id)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`p-4 rounded-lg border-2 transition-all text-left ${
                      selectedReport === report.id
                        ? 'border-theme-accent-primary bg-theme-accent-primary/10'
                        : 'border-theme-border-primary hover:border-theme-accent-primary/50'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`w-10 h-10 ${report.color} rounded-lg flex items-center justify-center`}>
                        <report.icon className="text-theme-text-inverse" />
                      </div>
                      <h3 className="font-fredoka text-theme-text-primary">{report.title}</h3>
                    </div>
                    <p className="text-theme-text-secondary font-raleway text-sm">{report.description}</p>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Configuration */}
            <div className="admin-card rounded-2xl p-6">
              <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Report Configuration</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">Date Range</label>
                  <select
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                    className="w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none"
                  >
                    {dateRanges.map((range) => (
                      <option key={range.value} value={range.value}>{range.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">Format</label>
                  <select
                    value={format}
                    onChange={(e) => setFormat(e.target.value)}
                    className="w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none"
                  >
                    {formats.map((fmt) => (
                      <option key={fmt.value} value={fmt.value}>{fmt.label}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Preview & Generate */}
          <div className="space-y-6">
            {/* Report Preview */}
            <div className="admin-card rounded-2xl p-6">
              <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Report Preview</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-theme-text-secondary font-raleway text-sm">Report Type</span>
                  <span className="text-theme-text-primary font-raleway text-sm">{preview.title}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-theme-text-secondary font-raleway text-sm">Date Range</span>
                  <span className="text-theme-text-primary font-raleway text-sm">
                    {dateRanges.find(r => r.value === dateRange)?.label}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-theme-text-secondary font-raleway text-sm">Format</span>
                  <span className="text-theme-text-primary font-raleway text-sm">{format.toUpperCase()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-theme-text-secondary font-raleway text-sm">Est. Size</span>
                  <span className="text-theme-text-primary font-raleway text-sm">{preview.estimatedSize}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-theme-text-secondary font-raleway text-sm">Est. Time</span>
                  <span className="text-theme-text-primary font-raleway text-sm">{preview.estimatedTime}</span>
                </div>
              </div>
            </div>

            {/* Report Details */}
            <div className="admin-card rounded-2xl p-6">
              <h3 className="text-lg font-fredoka text-theme-text-primary mb-4">Report Contents</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <FaChartBar className="text-theme-accent-primary" />
                  <span className="text-theme-text-secondary font-raleway text-sm">{preview.charts} Charts & Graphs</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FaTable className="text-theme-accent-primary" />
                  <span className="text-theme-text-secondary font-raleway text-sm">{preview.tables} Data Tables</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FaUsers className="text-theme-accent-primary" />
                  <span className="text-theme-text-secondary font-raleway text-sm">{preview.dataPoints} Data Points</span>
                </div>
              </div>
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerateReport}
              disabled={generating}
              className={`w-full btn-primary py-3 rounded-lg font-raleway font-semibold flex items-center justify-center space-x-2 ${
                generating ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {generating ? (
                <>
                  <FaSpinner className="animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <FaDownload />
                  <span>Generate Report</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Recent Reports */}
        <div className="admin-card rounded-2xl p-6">
          <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Recent Reports</h2>
          <div className="space-y-3">
            {[
              { name: 'Revenue Report - Last 30 Days', date: '2024-01-20', size: '2.1 MB', format: 'PDF' },
              { name: 'Orders Report - This Month', date: '2024-01-19', size: '1.8 MB', format: 'Excel' },
              { name: 'Customer Report - Last Week', date: '2024-01-18', size: '950 KB', format: 'CSV' }
            ].map((report, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-theme-bg-hover transition-colors">
                <div className="flex items-center space-x-3">
                  <FaFileExport className="text-theme-accent-primary" />
                  <div>
                    <h4 className="text-theme-text-primary font-raleway font-medium text-sm">{report.name}</h4>
                    <p className="text-theme-text-tertiary font-raleway text-xs">{report.date} • {report.size} • {report.format}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="text-theme-accent-primary hover:text-theme-accent-hover">
                    <FaEye />
                  </button>
                  <button className="text-theme-accent-primary hover:text-theme-accent-hover">
                    <FaDownload />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </SuperAdminLayout>
  );
};

export default Reports;
