import React from 'react';
import { Navigate, useParams } from 'react-router-dom';

// Simple route protection based on URL structure
const RouteProtection = ({ children, requiredRole }) => {
  const { restaurantName } = useParams();
  
  // For now, we'll use simple session-based protection
  // In a real app, this would check JWT tokens
  
  if (requiredRole === 'admin') {
    // Check if admin is logged in (simplified)
    const adminLoggedIn = sessionStorage.getItem('adminLoggedIn');
    if (!adminLoggedIn) {
      return <Navigate to="/admin/login" replace />;
    }
  }
  
  if (requiredRole === 'owner') {
    // Check if owner is logged in for this restaurant
    const currentOwner = sessionStorage.getItem('currentOwner');
    if (!currentOwner) {
      return <Navigate to={`/${restaurantName}/login`} replace />;
    }
    
    // Verify owner belongs to this restaurant
    const ownerData = JSON.parse(currentOwner);
    const ownerRestaurantSlug = ownerData.restaurantName.toLowerCase().replace(/\s+/g, '-');
    if (ownerRestaurantSlug !== restaurantName) {
      return <Navigate to={`/${restaurantName}/login`} replace />;
    }
  }
  
  return children;
};

export default RouteProtection;
