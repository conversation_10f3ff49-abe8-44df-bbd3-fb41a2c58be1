import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';
import {
  FaUtensils,
  FaStore,
  FaEye,
  FaEdit,
  FaTrash,
  FaPlus,
  FaFilter,
  FaSearch,
  FaRupeeSign,
  FaStar
} from 'react-icons/fa';
import ZoneAdminLayout from '../ZoneAdminLayout';

const ZoneMergedMenu = () => {
  const { zoneId } = useParams();
  const [loading, setLoading] = useState(true);
  const [menuItems, setMenuItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedShop, setSelectedShop] = useState('all');

  // Mock data for zone merged menu
  const mockMenuItems = [
    {
      id: 1,
      name: 'Margherita Pizza',
      description: 'Fresh tomatoes, mozzarella, basil',
      price: 450,
      category: 'Pizza',
      shopName: 'Pizza Corner',
      shopId: 'shop1',
      image: '/api/placeholder/300/200',
      isAvailable: true,
      rating: 4.8,
      orders: 156
    },
    {
      id: 2,
      name: 'Chicken Biryani',
      description: 'Aromatic basmati rice with tender chicken',
      price: 320,
      category: 'Main Course',
      shopName: 'Spice Garden',
      shopId: 'shop2',
      image: '/api/placeholder/300/200',
      isAvailable: true,
      rating: 4.6,
      orders: 203
    },
    {
      id: 3,
      name: 'Classic Burger',
      description: 'Beef patty with lettuce, tomato, cheese',
      price: 280,
      category: 'Burgers',
      shopName: 'Burger Barn',
      shopId: 'shop3',
      image: '/api/placeholder/300/200',
      isAvailable: false,
      rating: 4.4,
      orders: 89
    }
  ];

  const categories = ['all', 'Pizza', 'Main Course', 'Burgers', 'Beverages', 'Desserts'];
  const shops = ['all', 'Pizza Corner', 'Spice Garden', 'Burger Barn', 'Sushi Zen'];

  useEffect(() => {
    const loadMergedMenu = () => {
      setLoading(true);
      try {
        // In a real app, this would fetch merged menu from all shops in the zone
        setTimeout(() => {
          setMenuItems(mockMenuItems);
          setFilteredItems(mockMenuItems);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error loading merged menu:', error);
        setMenuItems([]);
        setFilteredItems([]);
        setLoading(false);
      }
    };

    loadMergedMenu();
  }, [zoneId]);

  useEffect(() => {
    let filtered = menuItems;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.shopName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by shop
    if (selectedShop !== 'all') {
      filtered = filtered.filter(item => item.shopName === selectedShop);
    }

    setFilteredItems(filtered);
  }, [searchQuery, selectedCategory, selectedShop, menuItems]);

  if (loading) {
    return (
      <ZoneAdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 spinner-theme rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-primary font-raleway">Loading Zone Menu...</p>
          </div>
        </div>
      </ZoneAdminLayout>
    );
  }

  return (
    <ZoneAdminLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-primary mb-2">
              Zone Merged Menu
            </h1>
            <p className="text-theme-text-secondary font-raleway">
              View all menu items from shops in your zone
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="admin-card rounded-xl p-4 sm:p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-text-tertiary" />
              <input
                type="text"
                placeholder="Search menu items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-theme rounded-lg pl-10 pr-4 py-2 w-full"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input-theme rounded-lg px-4 py-2"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>

            {/* Shop Filter */}
            <select
              value={selectedShop}
              onChange={(e) => setSelectedShop(e.target.value)}
              className="input-theme rounded-lg px-4 py-2"
            >
              {shops.map(shop => (
                <option key={shop} value={shop}>
                  {shop === 'all' ? 'All Shops' : shop}
                </option>
              ))}
            </select>

            {/* Results Count */}
            <div className="flex items-center justify-center admin-card rounded-lg px-4 py-2">
              <span className="text-theme-text-secondary font-raleway text-sm">
                {filteredItems.length} items found
              </span>
            </div>
          </div>
        </div>

        {/* Menu Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="admin-card rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300"
            >
              {/* Item Image */}
              <div className="relative h-48 bg-theme-bg-secondary">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-3 right-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-raleway ${
                    item.isAvailable 
                      ? 'bg-status-success text-white' 
                      : 'bg-status-error text-white'
                  }`}>
                    {item.isAvailable ? 'Available' : 'Unavailable'}
                  </span>
                </div>
              </div>

              {/* Item Details */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-lg font-fredoka text-theme-text-primary">
                    {item.name}
                  </h3>
                  <div className="flex items-center space-x-1">
                    <FaStar className="text-yellow-400 text-sm" />
                    <span className="text-theme-text-secondary text-sm font-raleway">
                      {item.rating}
                    </span>
                  </div>
                </div>

                <p className="text-theme-text-secondary font-raleway text-sm mb-3">
                  {item.description}
                </p>

                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-1">
                    <FaRupeeSign className="text-theme-accent-primary text-lg" />
                    <span className="text-xl font-fredoka text-theme-text-primary">
                      {item.price}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <FaStore className="text-theme-text-tertiary text-sm" />
                    <span className="text-theme-text-secondary text-sm font-raleway">
                      {item.shopName}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-theme-text-tertiary text-xs font-raleway">
                    {item.orders} orders
                  </span>
                  <div className="flex items-center space-x-2">
                    <button className="text-theme-text-secondary hover:text-theme-accent-primary transition-colors p-1">
                      <FaEye />
                    </button>
                    <button className="text-theme-text-secondary hover:text-theme-accent-primary transition-colors p-1">
                      <FaEdit />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <FaUtensils className="text-6xl text-theme-text-tertiary mx-auto mb-4" />
            <h3 className="text-xl font-fredoka text-theme-text-primary mb-2">
              No menu items found
            </h3>
            <p className="text-theme-text-secondary font-raleway">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </motion.div>
    </ZoneAdminLayout>
  );
};

export default ZoneMergedMenu;
