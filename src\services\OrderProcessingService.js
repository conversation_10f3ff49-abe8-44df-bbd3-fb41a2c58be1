// Order Processing Service - Handles data distribution to different user types

class OrderProcessingService {
  
  // Process order and distribute data according to restaurant/zone type
  static processOrder(orderData) {
    const {
      orderId,
      restaurantId,
      restaurantType,
      tableId,
      items,
      vendorBreakdown,
      paymentModel,
      grandTotal,
      timestamp
    } = orderData;

    if (restaurantType === 'single') {
      return this.processSingleRestaurantOrder(orderData);
    } else if (restaurantType === 'zone') {
      return this.processZoneOrder(orderData);
    }
  }

  // Handle Single Restaurant Mode
  static processSingleRestaurantOrder(orderData) {
    const {
      orderId,
      restaurantId,
      tableId,
      items,
      grandTotal,
      paymentMethod,
      specialInstructions,
      timestamp,
      sessionId
    } = orderData;

    // Create single order record for restaurant owner
    const restaurantOrder = {
      orderId,
      restaurantId,
      tableId,
      sessionId,
      items: items.map(item => ({
        itemId: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        specialInstructions: item.specialInstructions,
        subtotal: item.price * item.quantity
      })),
      totalItems: items.reduce((sum, item) => sum + item.quantity, 0),
      grandTotal,
      paymentMethod,
      paymentStatus: 'pending',
      specialInstructions,
      status: 'ordered',
      statusHistory: [{
        status: 'ordered',
        timestamp,
        updatedBy: 'customer'
      }],
      createdAt: timestamp,
      updatedAt: timestamp
    };

    // Store order for restaurant owner dashboard
    this.storeRestaurantOrder(restaurantId, restaurantOrder);

    // Store order analytics data
    this.updateRestaurantAnalytics(restaurantId, {
      orderId,
      revenue: grandTotal,
      itemCount: restaurantOrder.totalItems,
      timestamp
    });

    return {
      success: true,
      orderId,
      restaurantOrder,
      message: 'Order sent to restaurant'
    };
  }

  // Handle Food Street (Multi-Vendor Zone) Mode
  static processZoneOrder(orderData) {
    const {
      orderId,
      restaurantId: zoneId,
      tableId,
      vendorBreakdown,
      paymentModel,
      grandTotal,
      paymentMethod,
      specialInstructions,
      timestamp,
      sessionId
    } = orderData;

    // Create parent order for zone admin
    const zoneOrder = {
      orderId,
      zoneId,
      tableId,
      sessionId,
      vendorCount: vendorBreakdown.length,
      totalVendorOrders: vendorBreakdown.length,
      grandTotal,
      paymentMethod,
      paymentModel,
      specialInstructions,
      status: 'ordered',
      vendorBreakdown: vendorBreakdown.map(vendor => ({
        vendorId: vendor.vendorId,
        vendorName: vendor.vendorName,
        itemCount: vendor.items.length,
        subtotal: vendor.subtotal,
        status: vendor.status
      })),
      statusHistory: [{
        status: 'ordered',
        timestamp,
        updatedBy: 'customer'
      }],
      createdAt: timestamp,
      updatedAt: timestamp
    };

    // Store zone order for zone admin dashboard
    this.storeZoneOrder(zoneId, zoneOrder);

    // Create individual vendor orders
    const vendorOrders = vendorBreakdown.map(vendor => {
      const vendorOrder = {
        orderId: `${orderId}_${vendor.vendorId}`,
        parentOrderId: orderId,
        vendorId: vendor.vendorId,
        vendorName: vendor.vendorName,
        zoneId,
        tableId,
        sessionId,
        items: vendor.items.map(item => ({
          itemId: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          specialInstructions: item.specialInstructions,
          subtotal: item.price * item.quantity
        })),
        totalItems: vendor.items.reduce((sum, item) => sum + item.quantity, 0),
        subtotal: vendor.subtotal,
        paymentMethod,
        paymentModel,
        paymentStatus: paymentModel === 'shop-wise' ? 'pending' : 'auto-split',
        status: 'ordered',
        statusHistory: [{
          status: 'ordered',
          timestamp,
          updatedBy: 'customer'
        }],
        createdAt: timestamp,
        updatedAt: timestamp
      };

      // Store order for individual vendor dashboard
      this.storeVendorOrder(vendor.vendorId, vendorOrder);

      // Update vendor analytics
      this.updateVendorAnalytics(vendor.vendorId, {
        orderId: vendorOrder.orderId,
        revenue: vendor.subtotal,
        itemCount: vendorOrder.totalItems,
        timestamp
      });

      return vendorOrder;
    });

    // Update zone analytics
    this.updateZoneAnalytics(zoneId, {
      orderId,
      revenue: grandTotal,
      vendorCount: vendorBreakdown.length,
      totalItems: vendorOrders.reduce((sum, order) => sum + order.totalItems, 0),
      timestamp
    });

    return {
      success: true,
      orderId,
      zoneOrder,
      vendorOrders,
      message: `Order split among ${vendorBreakdown.length} vendors`
    };
  }

  // Storage methods for different user types
  static storeRestaurantOrder(restaurantId, order) {
    const existingOrders = JSON.parse(localStorage.getItem(`restaurant_orders_${restaurantId}`) || '[]');
    existingOrders.unshift(order); // Add to beginning for latest first
    localStorage.setItem(`restaurant_orders_${restaurantId}`, JSON.stringify(existingOrders));
    
    // Also store in real-time orders for live dashboard
    const liveOrders = JSON.parse(localStorage.getItem(`live_orders_${restaurantId}`) || '[]');
    liveOrders.unshift(order);
    localStorage.setItem(`live_orders_${restaurantId}`, JSON.stringify(liveOrders));
  }

  static storeZoneOrder(zoneId, order) {
    const existingOrders = JSON.parse(localStorage.getItem(`zone_orders_${zoneId}`) || '[]');
    existingOrders.unshift(order);
    localStorage.setItem(`zone_orders_${zoneId}`, JSON.stringify(existingOrders));
    
    // Store in live orders for zone admin
    const liveOrders = JSON.parse(localStorage.getItem(`zone_live_orders_${zoneId}`) || '[]');
    liveOrders.unshift(order);
    localStorage.setItem(`zone_live_orders_${zoneId}`, JSON.stringify(liveOrders));
  }

  static storeVendorOrder(vendorId, order) {
    const existingOrders = JSON.parse(localStorage.getItem(`vendor_orders_${vendorId}`) || '[]');
    existingOrders.unshift(order);
    localStorage.setItem(`vendor_orders_${vendorId}`, JSON.stringify(existingOrders));
    
    // Store in live orders for vendor
    const liveOrders = JSON.parse(localStorage.getItem(`vendor_live_orders_${vendorId}`) || '[]');
    liveOrders.unshift(order);
    localStorage.setItem(`vendor_live_orders_${vendorId}`, JSON.stringify(liveOrders));
  }

  // Analytics update methods
  static updateRestaurantAnalytics(restaurantId, data) {
    const analytics = JSON.parse(localStorage.getItem(`restaurant_analytics_${restaurantId}`) || '{}');
    
    // Update daily stats
    const today = new Date().toISOString().split('T')[0];
    if (!analytics[today]) {
      analytics[today] = { orders: 0, revenue: 0, items: 0 };
    }
    
    analytics[today].orders += 1;
    analytics[today].revenue += data.revenue;
    analytics[today].items += data.itemCount;
    
    localStorage.setItem(`restaurant_analytics_${restaurantId}`, JSON.stringify(analytics));
  }

  static updateZoneAnalytics(zoneId, data) {
    const analytics = JSON.parse(localStorage.getItem(`zone_analytics_${zoneId}`) || '{}');
    
    const today = new Date().toISOString().split('T')[0];
    if (!analytics[today]) {
      analytics[today] = { orders: 0, revenue: 0, vendors: 0, items: 0 };
    }
    
    analytics[today].orders += 1;
    analytics[today].revenue += data.revenue;
    analytics[today].vendors += data.vendorCount;
    analytics[today].items += data.totalItems;
    
    localStorage.setItem(`zone_analytics_${zoneId}`, JSON.stringify(analytics));
  }

  static updateVendorAnalytics(vendorId, data) {
    const analytics = JSON.parse(localStorage.getItem(`vendor_analytics_${vendorId}`) || '{}');
    
    const today = new Date().toISOString().split('T')[0];
    if (!analytics[today]) {
      analytics[today] = { orders: 0, revenue: 0, items: 0 };
    }
    
    analytics[today].orders += 1;
    analytics[today].revenue += data.revenue;
    analytics[today].items += data.itemCount;
    
    localStorage.setItem(`vendor_analytics_${vendorId}`, JSON.stringify(analytics));
  }

  // Order status update methods
  static updateOrderStatus(orderId, newStatus, updatedBy, userType, userId) {
    if (userType === 'restaurant') {
      this.updateRestaurantOrderStatus(userId, orderId, newStatus, updatedBy);
    } else if (userType === 'zone') {
      this.updateZoneOrderStatus(userId, orderId, newStatus, updatedBy);
    } else if (userType === 'vendor') {
      this.updateVendorOrderStatus(userId, orderId, newStatus, updatedBy);
    }
  }

  static updateRestaurantOrderStatus(restaurantId, orderId, newStatus, updatedBy) {
    const orders = JSON.parse(localStorage.getItem(`restaurant_orders_${restaurantId}`) || '[]');
    const liveOrders = JSON.parse(localStorage.getItem(`live_orders_${restaurantId}`) || '[]');
    
    const updateOrder = (orderList) => {
      return orderList.map(order => {
        if (order.orderId === orderId) {
          return {
            ...order,
            status: newStatus,
            updatedAt: new Date().toISOString(),
            statusHistory: [
              ...order.statusHistory,
              {
                status: newStatus,
                timestamp: new Date().toISOString(),
                updatedBy
              }
            ]
          };
        }
        return order;
      });
    };

    localStorage.setItem(`restaurant_orders_${restaurantId}`, JSON.stringify(updateOrder(orders)));
    localStorage.setItem(`live_orders_${restaurantId}`, JSON.stringify(updateOrder(liveOrders)));
  }

  static updateVendorOrderStatus(vendorId, orderId, newStatus, updatedBy) {
    const orders = JSON.parse(localStorage.getItem(`vendor_orders_${vendorId}`) || '[]');
    const liveOrders = JSON.parse(localStorage.getItem(`vendor_live_orders_${vendorId}`) || '[]');
    
    const updateOrder = (orderList) => {
      return orderList.map(order => {
        if (order.orderId === orderId) {
          return {
            ...order,
            status: newStatus,
            updatedAt: new Date().toISOString(),
            statusHistory: [
              ...order.statusHistory,
              {
                status: newStatus,
                timestamp: new Date().toISOString(),
                updatedBy
              }
            ]
          };
        }
        return order;
      });
    };

    localStorage.setItem(`vendor_orders_${vendorId}`, JSON.stringify(updateOrder(orders)));
    localStorage.setItem(`vendor_live_orders_${vendorId}`, JSON.stringify(updateOrder(liveOrders)));
  }

  // Get orders for different user types
  static getOrdersForUser(userType, userId) {
    if (userType === 'restaurant') {
      return JSON.parse(localStorage.getItem(`restaurant_orders_${userId}`) || '[]');
    } else if (userType === 'zone') {
      return JSON.parse(localStorage.getItem(`zone_orders_${userId}`) || '[]');
    } else if (userType === 'vendor') {
      return JSON.parse(localStorage.getItem(`vendor_orders_${userId}`) || '[]');
    }
    return [];
  }

  static getLiveOrdersForUser(userType, userId) {
    if (userType === 'restaurant') {
      return JSON.parse(localStorage.getItem(`live_orders_${userId}`) || '[]');
    } else if (userType === 'zone') {
      return JSON.parse(localStorage.getItem(`zone_live_orders_${userId}`) || '[]');
    } else if (userType === 'vendor') {
      return JSON.parse(localStorage.getItem(`vendor_live_orders_${userId}`) || '[]');
    }
    return [];
  }
}

export default OrderProcessingService;
