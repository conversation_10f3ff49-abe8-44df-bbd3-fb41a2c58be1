import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { loginUser, clearError } from '../store/slices/authSlice';
import { FaUser, Fa<PERSON>ock, FaEye, FaEyeSlash } from 'react-icons/fa';
import LocalStorageService from '../services/LocalStorageService';


const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  // This is now only for staff login (4 user types)
  const [showPassword, setShowPassword] = useState(false);

  // Debug function to check restaurant data (call manually)
  const debugRestaurantData = () => {
    console.log('=== RESTAURANT DATA DEBUG ===');
    const restaurants = LocalStorageService.getRestaurants();
    console.log('All restaurants:', restaurants);

    restaurants.forEach((restaurant, index) => {
      console.log(`Restaurant ${index + 1}:`, {
        name: restaurant.name,
        id: restaurant.id,
        status: restaurant.status,
        loginCredentials: restaurant.loginCredentials
      });
    });
  };



  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.auth);



  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('=== LOGIN ATTEMPT ===');
    console.log('Credentials:', formData);

    try {
      const result = await dispatch(loginUser(formData));
      console.log('Login result:', result);

      if (result.type === 'auth/loginUser/fulfilled') {
        const userRole = result.payload.user.role;
        console.log('Login successful!');
        console.log('User:', result.payload.user);
        console.log('Role:', userRole);

        switch (userRole) {
          case 'admin':
            console.log('Navigating to admin dashboard');
            navigate('/tableserve/admin/dashboard');
            break;
          case 'restaurant_owner':
            const restaurantId = result.payload.user.restaurantId;
            console.log('Navigating to restaurant dashboard, ID:', restaurantId);
            console.log('URL will be:', `/tableserve/restaurant/${restaurantId}/dashboard`);

            // Use setTimeout to ensure state is updated before navigation
            setTimeout(() => {
              console.log('Executing navigation after state update');
              navigate(`/tableserve/restaurant/${restaurantId}/dashboard`, { replace: true });
            }, 100);
            break;
          case 'zone_admin':
            const zoneId = result.payload.user.zoneId;
            console.log('Navigating to zone dashboard, ID:', zoneId);
            navigate(`/tableserve/zone/${zoneId}/dashboard`);
            break;
          default:
            console.log('Unknown role, staying on login');
            navigate('/tableserve/login');
            break;
        }
      } else {
        console.log('Login failed:', result);
        if (result.error) {
          console.log('Error details:', result.error);
        }
        if (result.payload) {
          console.log('Error payload:', result.payload);
        }
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    if (error) dispatch(clearError());
  };

  return (
    <div className="min-h-screen bg-accent/80 backdrop-blur-lg flex items-center justify-center px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white  p-8 rounded-2xl shadow-2xl w-full max-w-md"
      >
        <div className="text-center mb-8">
          <h1 className="text-3xl font-fredoka text-accent mb-2">TableServe</h1>
          <p className="text-primary-bg font-raleway"> Login </p>

        </div>

        <form onSubmit={handleSubmit} className="space-y-6" autoComplete="off">
          {/* Staff Login Fields */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <FaUser className="w-4 h-4 text-primary-bg" />
            </div>
            <input
              type="text"
              name="username"
              placeholder="Username"
              value={formData.username}
              onChange={handleChange}
              className="block w-full pl-10 pr-3 py-3 border border-primary-bg rounded-lg bg-white/10 text-primary-bg placeholder-primary-bg focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
              autoComplete="off"
              required
            />
          </div>

          <div style={{ position: 'relative', display: 'block' }}>
            {/* Lock Icon */}
            <div style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              pointerEvents: 'none'
            }}>
              <FaLock className="w-4 h-4 text-primary-bg" />
            </div>

            {/* Password Input */}
            <input
              type={showPassword ? 'text' : 'password'}
              name="password"
              placeholder="Password"
              value={formData.password}
              onChange={handleChange}
              className="block w-full py-3 border border-primary-bg rounded-lg bg-white/10 text-primary-bg placeholder-primary-bg focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
              style={{
                paddingLeft: '40px',
                paddingRight: '40px',
                position: 'relative',
                zIndex: 1
              }}
              autoComplete="off"
              required
            />

            {/* Eye Icon Button */}
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 3,
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '4px'
              }}
              className="text-primary-bg hover:text-primary-bg focus:outline-none focus:text-primary-bg transition-colors"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? (
                <FaEyeSlash className="w-4 h-4" />
              ) : (
                <FaEye className="w-4 h-4" />
              )}
            </button>
          </div>

          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-red-400 text-sm font-raleway text-center"
            >
              {error}
            </motion.div>
          )}

          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={loading}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-3 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </motion.button>

          {/* Debug Test Button */}
          <button
            type="button"
            onClick={() => {
              setFormData({ username: 'balaji_admin', password: '8825549901' });
              console.log('Auto-filled credentials for testing');
            }}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white font-raleway font-semibold py-2 rounded-lg transition-colors mt-2"
          >
            Auto-Fill Test Credentials (balaji_admin/8825549901)
          </button>

        </form>



        {/* Customer Login Info */}
        <div className="mt-6 text-center">
          <p className="text-black font-raleway text-sm mb-2">Looking for customer access?</p>
          <p className="text-black font-raleway text-xs">
            Scan the QR code at your restaurant table to access the menu and place orders.
          </p>
        </div>

        <div className="mt-6 text-center">
          <button className="text-accent hover:text-accent/80 font-raleway text-sm">
            Forgot your password?
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default Login;
