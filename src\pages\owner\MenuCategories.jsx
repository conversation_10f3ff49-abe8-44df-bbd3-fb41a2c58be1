import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';
import {
  FaList,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaEyeSlash,
  FaTimes,
  FaImage
} from 'react-icons/fa';
import SingleRestaurantLayout from '../../components/owner/SingleRestaurantLayout';
import ImageUpload from '../../components/common/ImageUpload';

const MenuCategories = () => {
  const { restaurantId } = useParams();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    image: '',
    isActive: true,
    sortOrder: 0
  });

  // Mock categories data
  const mockCategories = [
    {
      id: 1,
      name: 'Appetizers',
      description: 'Start your meal with our delicious appetizers',
      image: '/images/appetizers.jpg',
      isActive: true,
      sortOrder: 1,
      itemCount: 8
    },
    {
      id: 2,
      name: 'Main Courses',
      description: 'Hearty and satisfying main dishes',
      image: '/images/mains.jpg',
      isActive: true,
      sortOrder: 2,
      itemCount: 15
    },
    {
      id: 3,
      name: 'Desserts',
      description: 'Sweet treats to end your meal',
      image: '/images/desserts.jpg',
      isActive: true,
      sortOrder: 3,
      itemCount: 6
    },
    {
      id: 4,
      name: 'Beverages',
      description: 'Refreshing drinks and beverages',
      image: '/images/beverages.jpg',
      isActive: false,
      sortOrder: 4,
      itemCount: 12
    }
  ];

  useEffect(() => {
    loadCategories();
  }, [restaurantId]);

  const loadCategories = () => {
    try {
      const savedCategories = localStorage.getItem(`categories_${restaurantId}`);
      if (savedCategories) {
        setCategories(JSON.parse(savedCategories));
      } else {
        setCategories(mockCategories);
        localStorage.setItem(`categories_${restaurantId}`, JSON.stringify(mockCategories));
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      setCategories(mockCategories);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = () => {
    setEditingCategory(null);
    setFormData({
      name: '',
      description: '',
      image: '',
      isActive: true,
      sortOrder: categories.length + 1
    });
    setShowModal(true);
  };

  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description,
      image: category.image,
      isActive: category.isActive,
      sortOrder: category.sortOrder
    });
    setShowModal(true);
  };

  const handleSaveCategory = () => {
    try {
      let updatedCategories;

      if (editingCategory) {
        updatedCategories = categories.map(category =>
          category.id === editingCategory.id
            ? { ...category, ...formData }
            : category
        );
      } else {
        const newCategory = {
          id: Date.now(),
          ...formData,
          itemCount: 0
        };
        updatedCategories = [...categories, newCategory];
      }

      setCategories(updatedCategories);
      localStorage.setItem(`categories_${restaurantId}`, JSON.stringify(updatedCategories));
      setShowModal(false);
    } catch (error) {
      console.error('Error saving category:', error);
    }
  };

  const handleDeleteCategory = (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category? This will also remove all items in this category.')) {
      const updatedCategories = categories.filter(category => category.id !== categoryId);
      setCategories(updatedCategories);
      localStorage.setItem(`categories_${restaurantId}`, JSON.stringify(updatedCategories));
    }
  };

  const toggleCategoryStatus = (categoryId) => {
    const updatedCategories = categories.map(category =>
      category.id === categoryId
        ? { ...category, isActive: !category.isActive }
        : category
    );
    setCategories(updatedCategories);
    localStorage.setItem(`categories_${restaurantId}`, JSON.stringify(updatedCategories));
  };

  if (loading) {
    return (
      <SingleRestaurantLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-primary font-raleway">Loading categories...</p>
          </div>
        </div>
      </SingleRestaurantLayout>
    );
  }

  return (
    <SingleRestaurantLayout>
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2">Menu Categories</h1>
            <p className="text-secondary font-raleway text-sm sm:text-base">Organize your menu items into categories</p>
          </div>

          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAddCategory}
            className="mt-4 sm:mt-0 btn-primary px-6 py-3 rounded-xl font-raleway font-semibold flex items-center space-x-2"
          >
            <FaPlus />
            <span>Add Category</span>
          </motion.button>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="admin-card rounded-2xl overflow-hidden hover:border-accent/30 transition-all duration-300"
            >
              {/* Category Image */}
              <div className="h-48 bg-secondary relative overflow-hidden">
                {category.image ? (
                  <img
                    src={category.image}
                    alt={category.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <FaImage className="text-4xl text-tertiary" />
                  </div>
                )}

                {/* Status Badge */}
                <div className="absolute top-4 right-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-raleway ${category.isActive
                    ? 'bg-green-500 text-white'
                    : 'bg-red-500 text-white'
                    }`}>
                    {category.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>

                {/* Sort Order */}
                <div className="absolute top-4 left-4">
                  <span className="bg-black/50 text-white px-2 py-1 rounded text-sm font-raleway">
                    #{category.sortOrder}
                  </span>
                </div>
              </div>

              {/* Category Info */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-fredoka text-primary">{category.name}</h3>
                  <span className="text-accent font-raleway text-sm">{category.itemCount} items</span>
                </div>

                <p className="text-secondary font-raleway text-sm mb-4 line-clamp-2">
                  {category.description || 'No description provided'}
                </p>

                {/* Actions */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEditCategory(category)}
                    className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg font-raleway text-sm flex items-center justify-center space-x-1"
                  >
                    <FaEdit />
                    <span>Edit</span>
                  </button>

                  <button
                    onClick={() => toggleCategoryStatus(category.id)}
                    className={`flex-1 py-2 rounded-lg font-raleway text-sm flex items-center justify-center space-x-1 ${category.isActive
                      ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
                      : 'bg-green-500 hover:bg-green-600 text-white'
                      }`}
                  >
                    {category.isActive ? <FaEyeSlash /> : <FaEye />}
                    <span>{category.isActive ? 'Hide' : 'Show'}</span>
                  </button>

                  <button
                    onClick={() => handleDeleteCategory(category.id)}
                    className="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg font-raleway text-sm flex items-center justify-center space-x-1"
                  >
                    <FaTrash />
                    <span>Delete</span>
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {categories.length === 0 && (
          <div className="text-center py-12">
            <FaList className="text-6xl text-tertiary mx-auto mb-4" />
            <h3 className="text-xl font-fredoka text-primary mb-2">No Categories Found</h3>
            <p className="text-secondary font-raleway mb-4">Start by creating your first menu category.</p>
            <button
              onClick={handleAddCategory}
              className="btn-primary px-6 py-3 rounded-xl font-raleway font-semibold"
            >
              Create First Category
            </button>
          </div>
        )}

        {/* Add/Edit Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="admin-card rounded-2xl p-6 h-full overflow-y-auto w-full max-w-md"
            >
              <div className="flex  items-center justify-between mb-6">
                <h2 className="text-xl font-fredoka text-primary">
                  {editingCategory ? 'Edit Category' : 'Add New Category'}
                </h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-tertiary hover:text-primary"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-primary font-raleway font-medium mb-2">Category Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="e.g., Appetizers, Main Courses"
                    className="input-theme w-full px-4 py-3 rounded-lg font-raleway"
                  />
                </div>

                <div>
                  <label className="block text-primary font-raleway font-medium mb-2">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Brief description of this category"
                    rows="3"
                    className="input-theme w-full px-4 py-3 rounded-lg font-raleway"
                  />
                </div>

                <div className='text-primary'>
                  <label className="block text-secondary  font-raleway font-medium mb-2">Category Image</label>
                  <ImageUpload
                    currentImage={formData.image}
                    onImageChange={(imageUrl) => setFormData({ ...formData, image: imageUrl })}
                    label="Upload category image"
                    size="large"
                    shape="rounded"
                    className='admin-card p-4 rounded-2xl'
                  />
                </div>

                <div>
                  <label className="block text-primary font-raleway font-medium mb-2">Sort Order</label>
                  <input
                    type="number"
                    min="1"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) })}
                    className="input-theme w-full px-4 py-3 rounded-lg font-raleway"
                  />
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="w-4 h-4 text-accent"
                  />
                  <label htmlFor="isActive" className="text-primary font-raleway">
                    Active (visible to customers)
                  </label>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowModal(false)}
                  className="flex-1 bg-secondary hover:bg-tertiary text-primary py-3 rounded-lg font-raleway"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveCategory}
                  className="flex-1 btn-primary py-3 rounded-lg font-raleway font-semibold"
                >
                  {editingCategory ? 'Update' : 'Create'} Category
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </SingleRestaurantLayout>
  );
};

export default MenuCategories;
