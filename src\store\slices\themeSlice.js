import { createSlice } from '@reduxjs/toolkit';

// Get initial theme from localStorage or default to 'dark'
const getInitialTheme = () => {
  if (typeof window !== 'undefined') {
    const savedTheme = localStorage.getItem('tableserve-theme');
    if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
      return savedTheme;
    }
    // Check system preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
      return 'light';
    }
  }
  return 'dark'; // Default to dark mode as admin is designed for dark
};

const initialState = {
  mode: getInitialTheme(),
  isTransitioning: false,
  preferences: {
    autoSwitchTime: null, // Future feature for auto dark/light switching
    followSystem: false,
  }
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.isTransitioning = true;
      state.mode = state.mode === 'dark' ? 'light' : 'dark';
      
      // Persist to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('tableserve-theme', state.mode);
        // Apply theme class to document
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(state.mode);
      }
    },
    
    setTheme: (state, action) => {
      const newTheme = action.payload;
      if (['light', 'dark'].includes(newTheme)) {
        state.isTransitioning = true;
        state.mode = newTheme;
        
        // Persist to localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('tableserve-theme', newTheme);
          // Apply theme class to document
          document.documentElement.classList.remove('light', 'dark');
          document.documentElement.classList.add(newTheme);
        }
      }
    },
    
    finishTransition: (state) => {
      state.isTransitioning = false;
    },
    
    updatePreferences: (state, action) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
    
    initializeTheme: (state) => {
      // Initialize theme on app load
      if (typeof window !== 'undefined') {
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(state.mode);
      }
    }
  }
});

export const { 
  toggleTheme, 
  setTheme, 
  finishTransition, 
  updatePreferences, 
  initializeTheme 
} = themeSlice.actions;

export default themeSlice.reducer;

// Selectors
export const selectTheme = (state) => state.theme.mode;
export const selectIsTransitioning = (state) => state.theme.isTransitioning;
export const selectThemePreferences = (state) => state.theme.preferences;
