import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams } from 'react-router-dom';
import {
  FaEdit,
  FaTimes,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaUser,
  FaStar,
  FaStore,
  FaUsers,
  FaRupeeSign,
  FaShieldAlt,
  FaCamera,
  FaGlobe,
  FaInstagram,
  FaFacebook,
  FaClock,
  FaCheck
} from 'react-icons/fa';
import ZoneAdminLayout from './ZoneAdminLayout';
import ImageUpload from '../common/ImageUpload';
import LocalStorageService from '../../services/LocalStorageService';
import ProfileOTPVerification from '../common/ProfileOTPVerification';

const ZoneProfile = () => {
  const { zoneId } = useParams();
  const [zoneData, setZoneData] = useState({});
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({});
  const [showOTPVerification, setShowOTPVerification] = useState(false);
  const [pendingChanges, setPendingChanges] = useState(null);

  useEffect(() => {
    const loadZoneProfile = () => {
      setLoading(true);
      try {
        const profile = LocalStorageService.getZoneProfile(zoneId);
        if (profile) {
          setZoneData(profile);
          setFormData(profile);
        } else {
          // Set default empty profile
          const defaultProfile = {
            id: zoneId,
            name: '',
            description: '',
            logo: '',
            coverImage: '',
            ownerName: '',
            ownerPhone: '',
            ownerEmail: '',
            address: '',
            city: '',
            state: '',
            zipCode: '',
            operatingHours: {
              monday: { open: '10:00', close: '22:00', closed: false },
              tuesday: { open: '10:00', close: '22:00', closed: false },
              wednesday: { open: '10:00', close: '22:00', closed: false },
              thursday: { open: '10:00', close: '22:00', closed: false },
              friday: { open: '10:00', close: '23:00', closed: false },
              saturday: { open: '10:00', close: '23:00', closed: false },
              sunday: { open: '11:00', close: '21:00', closed: false }
            },
            status: 'active',
            rating: 0,
            totalVendors: 0,
            totalTables: 0,
            totalRevenue: 0,
            joinDate: new Date().toISOString().split('T')[0],
            features: [],
            socialMedia: {
              website: '',
              instagram: '',
              facebook: ''
            }
          };
          setZoneData(defaultProfile);
          setFormData(defaultProfile);
        }
      } catch (error) {
        console.error('Error loading zone profile:', error);
      } finally {
        setLoading(false);
      }
    };

    if (zoneId) {
      loadZoneProfile();
    }
  }, [zoneId]);

  const handleSave = () => {
    // Store pending changes and show OTP verification
    setPendingChanges(formData);
    setShowOTPVerification(true);
  };

  const handleOTPVerified = () => {
    try {
      // Apply the pending changes after OTP verification
      const updatedProfile = LocalStorageService.updateZoneProfile(zoneId, pendingChanges);
      if (updatedProfile) {
        setZoneData(updatedProfile);
        setEditing(false);
        setPendingChanges(null);
        alert('Profile updated successfully!');
      }
    } catch (error) {
      console.error('Error saving zone profile:', error);
      alert('Failed to update profile. Please try again.');
    }
  };

  const handleOTPCancel = () => {
    setShowOTPVerification(false);
    setPendingChanges(null);
  };

  const handleCancel = () => {
    setFormData(zoneData);
    setEditing(false);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOperatingHoursChange = (day, field, value) => {
    setFormData(prev => ({
      ...prev,
      operatingHours: {
        ...(prev.operatingHours || {}),
        [day]: {
          ...(prev.operatingHours?.[day] || { open: '09:00', close: '22:00', closed: false }),
          [field]: value
        }
      }
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'status-success bg-status-success-light';
      case 'inactive': return 'status-error bg-status-error-light';
      case 'maintenance': return 'status-warning bg-status-warning-light';
      default: return 'text-theme-text-tertiary bg-theme-bg-secondary';
    }
  };

  if (loading) {
    return (
      <ZoneAdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-theme-text-primary text-xl">Loading zone profile...</div>
        </div>
      </ZoneAdminLayout>
    );
  }

  return (
    <ZoneAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 lg:gap-6">
          <div>
            <h1 className="text-2xl lg:text-3xl font-fredoka text-theme-text-primary mb-2">Zone Profile</h1>
            <p className="text-theme-text-secondary font-raleway">Manage your food zone information and settings</p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <AnimatePresence mode="wait">
              {editing ? (
                <motion.div
                  key="edit-actions"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="flex flex-col sm:flex-row gap-3"
                >
                  <button
                    onClick={handleCancel}
                    className="btn-secondary px-4 py-3 rounded-lg font-raleway flex items-center justify-center space-x-2"
                  >
                    <FaTimes />
                    <span>Cancel</span>
                  </button>
                  <button
                    onClick={handleSave}
                    className="btn-primary px-4 py-3 rounded-lg font-raleway flex items-center justify-center space-x-2"
                  >
                    <FaShieldAlt />
                    <span>Save Changes (OTP Required)</span>
                  </button>
                </motion.div>
              ) : (
                <motion.button
                  key="edit-button"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  onClick={() => setEditing(true)}
                  className="btn-primary px-6 py-3 rounded-lg font-raleway flex items-center justify-center space-x-2"
                >
                  <FaEdit />
                  <span>Edit Profile</span>
                </motion.button>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Profile Header Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="admin-card rounded-2xl overflow-hidden"
        >


          {/* Profile Info */}
          <div className="p-6 lg:p-8">
            <div className="flex flex-col sm:flex-row sm:items-start gap-6">
              {/* Profile Picture */}
              <div className="flex-shrink-0 mx-auto sm:mx-0">
                <div className="relative">
                  {editing ? (
                    <div className="w-24 h-24 lg:w-32 lg:h-32">
                      <ImageUpload
                        currentImage={formData.logo}
                        onImageChange={(imageUrl) => handleInputChange('logo', imageUrl)}
                        label=""
                        size="large"
                        shape="circle"
                        className="w-full h-full"
                      />
                    </div>
                  ) : (
                    <div className="w-24 h-24 lg:w-32 lg:h-32 rounded-full bg-theme-accent-primary/20 flex items-center justify-center overflow-hidden border-4 border-theme-bg-primary shadow-lg">
                      {zoneData.logo ? (
                        <img
                          src={zoneData.logo}
                          alt="Zone Logo"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <FaStore className="text-theme-accent-primary text-3xl lg:text-4xl" />
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Zone Info */}
              <div className="flex-1 text-center sm:text-left">
                <h2 className="text-2xl lg:text-3xl font-fredoka text-theme-text-primary mb-2">
                  {zoneData.name || 'Zone Name'}
                </h2>
                <p className="text-theme-text-secondary font-raleway mb-4 text-base">
                  {zoneData.address || 'Zone Address'}
                </p>

                <div className="flex flex-wrap items-center justify-center sm:justify-start gap-3">
                  <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getStatusColor(zoneData.status)}`}>
                    {zoneData.status || 'active'}
                  </span>
                  <div className="flex items-center space-x-1">
                    <FaStar className="text-status-warning" />
                    <span className="text-theme-text-primary font-raleway font-medium">
                      {(zoneData.rating || 0).toFixed(1)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1 text-theme-text-secondary">
                    <FaStore />
                    <span className="font-raleway text-sm">{zoneData.totalVendors || 0} Vendors</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {[
            {
              icon: FaStore,
              value: zoneData.totalVendors || 0,
              label: 'Vendors',
              color: 'text-status-info',
              bgColor: 'bg-status-info/10'
            },
            {
              icon: FaUsers,
              value: zoneData.totalTables || 0,
              label: 'Tables',
              color: 'text-status-warning',
              bgColor: 'bg-status-warning/10'
            },
            {
              icon: FaRupeeSign,
              value: `₹${(zoneData.totalRevenue || 0).toLocaleString()}`,
              label: 'Total Revenue',
              color: 'text-status-success',
              bgColor: 'bg-status-success/10'
            },
            {
              icon: FaStar,
              value: (zoneData.rating || 0).toFixed(1),
              label: 'Zone Rating',
              color: 'text-theme-accent-primary',
              bgColor: 'bg-theme-accent-primary/10'
            }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="admin-card rounded-xl p-4 lg:p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center space-x-3 lg:space-x-4">
                <div className={`w-12 h-12 lg:w-14 lg:h-14 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                  <stat.icon className={`${stat.color} text-xl lg:text-2xl`} />
                </div>
                <div>
                  <h3 className="text-lg lg:text-xl font-fredoka text-theme-text-primary">
                    {stat.value}
                  </h3>
                  <p className="text-theme-text-secondary font-raleway text-sm">
                    {stat.label}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="admin-card rounded-2xl p-6 lg:p-8"
          >
            <h3 className="text-xl lg:text-2xl font-fredoka text-theme-text-primary mb-6 flex items-center space-x-2">
              <FaUser className="text-theme-accent-primary" />
              <span>Basic Information</span>
            </h3>

            <div className="space-y-6">
              <div>
                <label className="block text-theme-text-primary font-raleway font-medium mb-2">Zone Name</label>
                <AnimatePresence mode="wait">
                  {editing ? (
                    <motion.input
                      key="name-input"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      type="text"
                      value={formData.name || ''}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                      placeholder="Enter zone name"
                    />
                  ) : (
                    <motion.p
                      key="name-display"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="text-theme-text-secondary font-raleway bg-theme-bg-secondary rounded-lg px-4 py-3"
                    >
                      {zoneData.name || 'Not specified'}
                    </motion.p>
                  )}
                </AnimatePresence>
              </div>

              <div>
                <label className="block text-theme-text-primary font-raleway font-medium mb-2">Description</label>
                <AnimatePresence mode="wait">
                  {editing ? (
                    <motion.textarea
                      key="description-input"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      value={formData.description || ''}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                      rows="4"
                      placeholder="Enter zone description"
                    />
                  ) : (
                    <motion.p
                      key="description-display"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="text-theme-text-secondary font-raleway bg-theme-bg-secondary rounded-lg px-4 py-3 min-h-[100px]"
                    >
                      {zoneData.description || 'No description provided'}
                    </motion.p>
                  )}
                </AnimatePresence>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">City</label>
                  <AnimatePresence mode="wait">
                    {editing ? (
                      <motion.input
                        key="city-input"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        type="text"
                        value={formData.city || ''}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                        placeholder="Enter city"
                      />
                    ) : (
                      <motion.p
                        key="city-display"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="text-theme-text-secondary font-raleway bg-theme-bg-secondary rounded-lg px-4 py-3"
                      >
                        {zoneData.city || 'Not specified'}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>
                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">State</label>
                  <AnimatePresence mode="wait">
                    {editing ? (
                      <motion.input
                        key="state-input"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        type="text"
                        value={formData.state || ''}
                        onChange={(e) => handleInputChange('state', e.target.value)}
                        className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                        placeholder="Enter state"
                      />
                    ) : (
                      <motion.p
                        key="state-display"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="text-theme-text-secondary font-raleway bg-theme-bg-secondary rounded-lg px-4 py-3"
                      >
                        {zoneData.state || 'Not specified'}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              <div>
                <label className="block text-theme-text-primary font-raleway font-medium mb-2">Full Address</label>
                <AnimatePresence mode="wait">
                  {editing ? (
                    <motion.textarea
                      key="address-input"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      value={formData.address || ''}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                      rows="3"
                      placeholder="Enter full address"
                    />
                  ) : (
                    <motion.div
                      key="address-display"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-start space-x-3 bg-theme-bg-secondary rounded-lg px-4 py-3"
                    >
                      <FaMapMarkerAlt className="text-theme-accent-primary mt-1 flex-shrink-0" />
                      <p className="text-theme-text-secondary font-raleway">
                        {zoneData.address || 'No address provided'}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="admin-card rounded-2xl p-6 lg:p-8"
          >
            <h3 className="text-xl lg:text-2xl font-fredoka text-theme-text-primary mb-6 flex items-center space-x-2">
              <FaPhone className="text-theme-accent-primary" />
              <span>Contact Information</span>
            </h3>

            <div className="space-y-6">
              <div>
                <label className="block text-theme-text-primary font-raleway font-medium mb-2">Owner Name</label>
                <AnimatePresence mode="wait">
                  {editing ? (
                    <motion.input
                      key="owner-name-input"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      type="text"
                      value={formData.ownerName || ''}
                      onChange={(e) => handleInputChange('ownerName', e.target.value)}
                      className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                      placeholder="Enter owner name"
                    />
                  ) : (
                    <motion.div
                      key="owner-name-display"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center space-x-3 bg-theme-bg-secondary rounded-lg px-4 py-3"
                    >
                      <FaUser className="text-theme-accent-primary" />
                      <p className="text-theme-text-secondary font-raleway">
                        {zoneData.ownerName || 'Not specified'}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <div>
                <label className="block text-theme-text-primary font-raleway font-medium mb-2">Phone Number</label>
                <AnimatePresence mode="wait">
                  {editing ? (
                    <motion.input
                      key="phone-input"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      type="tel"
                      value={formData.ownerPhone || ''}
                      onChange={(e) => handleInputChange('ownerPhone', e.target.value)}
                      className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                      placeholder="Enter phone number"
                    />
                  ) : (
                    <motion.div
                      key="phone-display"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center space-x-3 bg-theme-bg-secondary rounded-lg px-4 py-3"
                    >
                      <FaPhone className="text-theme-accent-primary" />
                      <p className="text-theme-text-secondary font-raleway">
                        {zoneData.ownerPhone || 'Not specified'}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <div>
                <label className="block text-theme-text-primary font-raleway font-medium mb-2">Email Address</label>
                <AnimatePresence mode="wait">
                  {editing ? (
                    <motion.input
                      key="email-input"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      type="email"
                      value={formData.ownerEmail || ''}
                      onChange={(e) => handleInputChange('ownerEmail', e.target.value)}
                      className="input-theme rounded-lg px-4 py-3 w-full font-raleway"
                      placeholder="Enter email address"
                    />
                  ) : (
                    <motion.div
                      key="email-display"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center space-x-3 bg-theme-bg-secondary rounded-lg px-4 py-3"
                    >
                      <FaEnvelope className="text-theme-accent-primary" />
                      <p className="text-theme-text-secondary font-raleway">
                        {zoneData.ownerEmail || 'Not specified'}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        </div>

        {/* OTP Verification Modal */}
        <ProfileOTPVerification
          isOpen={showOTPVerification}
          onClose={handleOTPCancel}
          onVerified={handleOTPVerified}
          phoneNumber={zoneData.ownerPhone}
          purpose="profile_update"
          entityId={zoneId}
          title="Verify Profile Update"
          description="For security, please verify your phone number to save profile changes."
        />
      </div>
    </ZoneAdminLayout>
  );
};

export default ZoneProfile;
