import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { FaPlus, FaMinus, FaTrash, FaArrowLeft } from 'react-icons/fa';
import { useParams, useNavigate } from 'react-router-dom';
import { CartContext } from '../../context/CartContext.jsx';

const CartScreen = () => {
  const { restaurantName, userId } = useParams();
  const navigate = useNavigate();
  const { cartItems, removeFromCart, updateCartItemQuantity } = useContext(CartContext);

  const updateQuantity = (id, delta) => {
    const item = cartItems.find(item => item.id === id);
    if (item) {
      const newQuantity = item.quantity + delta;
      if (newQuantity > 0) {
        updateCartItemQuantity(id, newQuantity);
      } else {
        removeFromCart(id);
      }
    }
  };

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => total + item.price * item.quantity, 0).toFixed(2);
  };

  const handleProceedToCheckout = () => {
    navigate(`/tableserve/${restaurantName}/${userId}/otp-login`);
  };

  return (
    <div className="min-h-screen bg-white text-primary-bg font-raleway p-4 max-w-md mx-auto">
      {/* Header */}
      <div className="flex items-center mb-6">
        <motion.button
          whileTap={{ scale: 0.9 }}
          onClick={() => navigate(-1)}
          className="p-3 bg-accent rounded-full shadow-lg "
        >
          <FaArrowLeft className="text-xl text-text-main" />
        </motion.button>
        <h1 className="text-2xl font-bold text-accent ml-6">My Cart</h1>
      </div>

      {cartItems.length === 0 ? (
        <div className="text-center text-accent text-lg mt-10">Your cart is empty.</div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl shadow-2xl shadow-black/40 backdrop-blur-md p-4 mb-6"
        >
          {cartItems.map(item => (
            <div key={item.id} className="flex items-center py-3 border-b border-divider-border last:border-b-0">
              <img src={item.image} alt={item.name} className="w-16 h-16 object-cover rounded-xl mr-3 shadow-md" />
              <div className="flex-1">
                <h3 className="text-base text-black font-bold">{item.name}</h3>
                <p className="text-black font-sans text-sm">₹{item.price.toFixed(2)}</p>
              </div>
              <div className="flex items-center">
                <motion.button
                  whileTap={{ scale: 0.9 }}
                  onClick={() => updateQuantity(item.id, -1)}
                  className="p-2 bg-accent rounded-full shadow-md"
                >
                  <FaMinus className="text-text-main text-xs" />
                </motion.button>
                <span className="text-base font-sans font-bold mx-2">{item.quantity}</span>
                <motion.button
                  whileTap={{ scale: 0.9 }}
                  onClick={() => updateQuantity(item.id, 1)}
                  className="p-2 bg-accent rounded-full shadow-md"
                >
                  <FaPlus className="text-text-main text-xs" />
                </motion.button>
                <motion.button
                  whileTap={{ scale: 0.9 }}
                  onClick={() => removeFromCart(item.id)}
                  className="ml-3 p-2 bg-red-700 rounded-full shadow-md"
                >
                  <FaTrash className="text-text-main text-xs" />
                </motion.button>
              </div>
            </div>
          ))}

          <div className="flex justify-between items-center pt-4 mt-4 ">
            <span className="text-xl font-raleway font-bold">Total:</span>
            <span className="text-2xl font-bold font-sans text-accent">₹{calculateTotal()}</span>
          </div>
        </motion.div>
      )}

      {cartItems.length > 0 && (
        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={handleProceedToCheckout}
          className="w-full bg-accent text-text-main py-4 rounded-2xl font-fredoka text-xl tracking-wide shadow-lg
                     hover:bg-hover-shade transition-colors duration-300"
        >
          Proceed to Checkout
        </motion.button>
      )}
    </div>
  );
};

export default CartScreen;