/* Theme Variables for TableServe Admin Interfaces */

:root {
  /* Transition properties for smooth theme switching */
  --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Theme (Default) */
.dark {
  /* Background Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #121212;
  --bg-tertiary: #1a1a1a;
  --bg-card: rgba(255, 255, 255, 0.05);
  --bg-hover: rgba(255, 255, 255, 0.1);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e5e5e5;
  --text-tertiary: #a3a3a3;
  --text-inverse: #0a0a0a;

  /* Border Colors */
  --border-primary: #2a2a2a;
  --border-secondary: #404040;
  --border-accent: rgba(255, 107, 0, 0.3);

  /* Accent Colors */
  --accent-primary: #FF6B00;
  --accent-secondary: #FF8533;
  --accent-hover: #E55A00;

  /* Sidebar specific */
  --sidebar-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  --sidebar-border: #2a2a2a;
  --sidebar-item-hover: rgba(255, 107, 0, 0.1);
  --sidebar-item-active: rgba(255, 107, 0, 0.2);

  /* Navbar specific */
  --navbar-bg: rgba(10, 10, 10, 0.95);
  --navbar-border: rgba(255, 255, 255, 0.1);
  --navbar-backdrop: blur(20px);

  /* Card specific */
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --card-border: rgba(255, 255, 255, 0.1);

  /* Input specific */
  --input-bg: rgba(255, 255, 255, 0.05);
  --input-border: rgba(255, 255, 255, 0.2);
  --input-border-focus: #FF6B00;
  --input-text: #ffffff;
  --input-placeholder: #9ca3af;

  /* Dropdown specific */
  --dropdown-bg: #1f2937;
  --dropdown-border: rgba(255, 255, 255, 0.1);
  --dropdown-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);

  /* Status colors */
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;

  /* Status background colors */
  --status-success-light: rgba(16, 185, 129, 0.2);
  --status-warning-light: rgba(245, 158, 11, 0.2);
  --status-error-light: rgba(239, 68, 68, 0.2);
  --status-info-light: rgba(59, 130, 246, 0.2);
}

/* Light Theme */
.light {
  /* Background Colors */
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-hover: rgba(0, 0, 0, 0.05);

  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #334155;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-accent: rgba(255, 107, 0, 0.2);

  /* Accent Colors */
  --accent-primary: #FF6B00;
  --accent-secondary: #FF8533;
  --accent-hover: #E55A00;

  /* Sidebar specific */
  --sidebar-bg: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  --sidebar-border: #e2e8f0;
  --sidebar-item-hover: rgba(255, 107, 0, 0.08);
  --sidebar-item-active: rgba(255, 107, 0, 0.15);

  /* Navbar specific */
  --navbar-bg: rgba(255, 255, 255, 0.95);
  --navbar-border: rgba(0, 0, 0, 0.1);
  --navbar-backdrop: blur(20px);

  /* Card specific */
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --card-border: #e2e8f0;

  /* Input specific */
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-border-focus: #FF6B00;
  --input-text: #111827;
  --input-placeholder: #9ca3af;

  /* Dropdown specific */
  --dropdown-bg: #ffffff;
  --dropdown-border: #e5e7eb;
  --dropdown-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Status colors */
  --status-success: #059669;
  --status-warning: #d97706;
  --status-error: #dc2626;
  --status-info: #2563eb;

  /* Status background colors */
  --status-success-light: rgba(5, 150, 105, 0.15);
  --status-warning-light: rgba(217, 119, 6, 0.15);
  --status-error-light: rgba(220, 38, 38, 0.15);
  --status-info-light: rgba(37, 99, 235, 0.15);
}

/* Transition classes for smooth theme switching */
.theme-transition {
  transition: var(--theme-transition);
}

.theme-transition * {
  transition: var(--theme-transition);
}

/* Component-specific theme classes */
.admin-layout {
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: var(--theme-transition);
}

.admin-sidebar {
  background: var(--sidebar-bg);
  border-color: var(--sidebar-border);
  transition: var(--theme-transition);
}

.admin-navbar {
  background: var(--navbar-bg);
  border-color: var(--navbar-border);
  backdrop-filter: var(--navbar-backdrop);
  transition: var(--theme-transition);
}

.admin-card {
  background: var(--bg-card);
  border-color: var(--card-border);
  box-shadow: var(--card-shadow);
  transition: var(--theme-transition);
}

.sidebar-item {
  transition: var(--theme-transition);
}

.sidebar-item:hover {
  background: var(--sidebar-item-hover);
}

.sidebar-item.active {
  background: var(--sidebar-item-active);
  color: var(--accent-primary);
}

/* Button variants */
.btn-primary {
  background: var(--accent-primary);
  color: var(--text-inverse);
  border-color: var(--accent-primary);
  transition: var(--theme-transition);
}

.btn-primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-primary);
  transition: var(--theme-transition);
}

.btn-secondary:hover {
  background: var(--bg-hover);
}

/* Input styles */
.input-theme {
  background: var(--input-bg);
  color: var(--input-text);
  border: 1px solid var(--input-border);
  transition: var(--theme-transition);
}

.input-theme:focus {
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.1);
  outline: none;
}

.input-theme::placeholder {
  color: var(--input-placeholder);
}

.input-theme:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--bg-secondary);
}

/* Dropdown styles */
.dropdown-theme {
  background: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  color: var(--text-primary);
  box-shadow: var(--dropdown-shadow);
  transition: var(--theme-transition);
}

/* Table styles */
.table-theme {
  background: var(--bg-card);
  color: var(--text-primary);
  transition: var(--theme-transition);
}

.table-theme th {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-color: var(--border-primary);
}

.table-theme td {
  border-color: var(--border-primary);
}

.table-theme tr:hover {
  background: var(--bg-hover);
}

/* Status indicators */
.status-success {
  color: var(--status-success);
}

.status-warning {
  color: var(--status-warning);
}

.status-error {
  color: var(--status-error);
}

.status-info {
  color: var(--status-info);
}

/* Loading spinner */
.spinner-theme {
  border-color: var(--border-primary);
  border-top-color: var(--accent-primary);
}

/* Scrollbar styling */
.scrollbar-theme::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-theme::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.scrollbar-theme::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.scrollbar-theme::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}

/* Additional utility classes for common patterns */
.bg-status-success {
  background-color: var(--status-success);
}

.bg-status-warning {
  background-color: var(--status-warning);
}

.bg-status-error {
  background-color: var(--status-error);
}

.bg-status-info {
  background-color: var(--status-info);
}

.bg-status-success-light {
  background-color: rgba(5, 150, 105, 0.2);
}

.bg-status-warning-light {
  background-color: rgba(217, 119, 6, 0.2);
}

.bg-status-error-light {
  background-color: rgba(220, 38, 38, 0.2);
}

.bg-status-info-light {
  background-color: rgba(37, 99, 235, 0.2);
}

/* Priority and category colors */
.text-priority-high {
  color: var(--status-error);
}

.text-priority-medium {
  color: var(--status-warning);
}

.text-priority-low {
  color: var(--status-success);
}

.text-category-technical {
  color: var(--status-error);
}

.text-category-qr {
  color: var(--status-info);
}

.text-category-profile {
  color: var(--status-success);
}

.text-category-menu {
  color: #a855f7;
  /* Purple for menu items */
}

/* Badge styles */
.badge-theme {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  transition: var(--theme-transition);
}

.badge-success {
  background: rgba(5, 150, 105, 0.2);
  color: var(--status-success);
  border-color: var(--status-success);
}

.badge-warning {
  background: rgba(217, 119, 6, 0.2);
  color: var(--status-warning);
  border-color: var(--status-warning);
}

.badge-error {
  background: rgba(220, 38, 38, 0.2);
  color: var(--status-error);
  border-color: var(--status-error);
}

.badge-info {
  background: rgba(37, 99, 235, 0.2);
  color: var(--status-info);
  border-color: var(--status-info);
}

/* Status color utility classes */
.status-success {
  color: var(--status-success);
}

.status-warning {
  color: var(--status-warning);
}

.status-error {
  color: var(--status-error);
}

.status-info {
  color: var(--status-info);
}

.bg-status-success-light {
  background-color: var(--status-success-light);
}

.bg-status-warning-light {
  background-color: var(--status-warning-light);
}

.bg-status-error-light {
  background-color: var(--status-error-light);
}

.bg-status-info-light {
  background-color: var(--status-info-light);
}

/* Enhanced text contrast classes */
.text-contrast-high {
  color: var(--text-primary);
  font-weight: 600;
}

.text-contrast-medium {
  color: var(--text-secondary);
  font-weight: 500;
}

.text-contrast-low {
  color: var(--text-tertiary);
  font-weight: 400;
}

/* Enhanced border classes for light mode visibility */
.border-visible {
  border: 1px solid var(--border-primary);
}

.border-visible-thick {
  border: 2px solid var(--border-primary);
}

.border-accent {
  border: 1px solid var(--accent-primary);
}

/* Enhanced shadow classes */
.shadow-visible {
  box-shadow: var(--card-shadow);
}

.shadow-strong {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Light mode specific overrides */
[data-theme="light"] .shadow-strong {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .border-visible {
  border-color: #d1d5db;
}

[data-theme="light"] .admin-card {
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Ensure proper contrast for interactive elements */
.interactive-element {
  transition: all 0.2s ease-in-out;
}

.interactive-element:hover {
  transform: translateY(-1px);
  box-shadow: var(--card-shadow);
}

/* Text utility classes for theme system */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-inverse {
  color: var(--text-inverse);
}

/* Background utility classes */
.bg-primary {
  background: var(--bg-primary);
}

.bg-secondary {
  background: var(--bg-secondary);
}

.bg-tertiary {
  background: var(--bg-tertiary);
}

.bg-card {
  background: var(--bg-card);
}

.bg-hover {
  background: var(--bg-hover);
}

/* Border utility classes */
.border-primary {
  border-color: var(--border-primary);
}

.border-secondary {
  border-color: var(--border-secondary);
}

.border-accent {
  border-color: var(--border-accent);
}

/* Accent utility classes */
.bg-accent {
  background: var(--accent-primary);
}

.text-accent {
  color: var(--accent-primary);
}

.border-accent {
  border-color: var(--accent-primary);
}