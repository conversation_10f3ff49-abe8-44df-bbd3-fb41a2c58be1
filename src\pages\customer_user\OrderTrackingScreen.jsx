import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaArrowLeft, FaUtensils, FaMotorcycle, FaCheckCircle, FaClock } from 'react-icons/fa';

const OrderTrackingScreen = () => {
  const { restaurantName, userId } = useParams();
  const navigate = useNavigate();

  // Simulate order status, time and details
  const orderStatus = 'Delivered'; // Can be 'Preparing', 'Out for Delivery', 'Delivered'
  const [currentTime, setCurrentTime] = React.useState(new Date());
  const estimatedTime = new Date(currentTime.getTime() + 30 * 60000); // 30 minutes from now

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const orderDetails = {
    items: [
      { name: 'Chicken Burger', quantity: 2, price: 12.99 },
      { name: 'French Fries', quantity: 1, price: 4.99 },
      { name: 'Coca Cola', quantity: 2, price: 2.99 }
    ],
    total: 36.95,
    orderTime: '12:30 PM',
    tableNumber: '15'
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Preparing': return 'text-highlight-chip';
      case 'Out for Delivery': return 'text-primary-accent';
      case 'Delivered': return 'text-green-400';
      default: return 'text-placeholder-subtext';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 font-raleway text-primary-bg overflow-hidden">
      {/* Header */}
      <div className="flex bg-accent items-center p-4 sm:p-6 shadow-lg">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => navigate(-1)}
          className="p-2.5 sm:p-3 bg-white/90 hover:bg-white rounded-full shadow-lg transition-all duration-300 hover:shadow-xl"
        >
          <FaArrowLeft className="text-lg sm:text-xl text-accent" />
        </motion.button>
        <div className="flex-1">
          <h1 className="text-xl sm:text-2xl text-white ml-6 sm:ml-8 font-semibold">Track Your Order</h1>
          <div className="flex items-center ml-6 sm:ml-8 mt-1">
            <FaClock className="text-white/80 text-sm" />
            <span className="text-white/80 text-sm ml-2">
              {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-2xl shadow-xl mt-4 sm:mt-6 mx-4 sm:mx-8 p-5 sm:p-6 text-center backdrop-blur-lg bg-white/90 relative overflow-hidden"
      >
        {/* Background Pattern */}
        <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
          <div className="absolute top-0 left-0 w-32 h-32 bg-accent rounded-full -translate-x-16 -translate-y-16"></div>
          <div className="absolute bottom-0 right-0 w-40 h-40 bg-highlight-chip rounded-full translate-x-20 translate-y-20"></div>
        </div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="relative z-10"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="text-left">
              <h2 className="text-lg sm:text-xl text-accent font-medium">Order #12345</h2>
              <p className="text-sm text-gray-500 mt-1">Table {orderDetails.tableNumber}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Ordered at</p>
              <p className="text-base font-medium text-accent">{orderDetails.orderTime}</p>
            </div>
          </div>
          
          <div className="bg-gray-50/50 rounded-xl p-4 mb-6">
            <p className={`text-xl sm:text-2xl font-bold ${getStatusColor(orderStatus)}`}>
              <motion.span
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 200 }}
              >
                {orderStatus}
              </motion.span>
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Estimated delivery by {estimatedTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </p>
          </div>

          <div className="bg-gray-50/50 rounded-xl p-4 mb-6">
            <h3 className="text-left text-sm font-medium text-gray-700 mb-3">Order Summary</h3>
            {orderDetails.items.map((item, index) => (
              <div key={index} className="flex justify-between items-center mb-2 text-sm">
                <div className="flex items-center">
                  <span className="text-gray-700">{item.quantity}x</span>
                  <span className="ml-2 text-gray-600">{item.name}</span>
                </div>
                <span className="text-gray-700">${(item.quantity * item.price).toFixed(2)}</span>
              </div>
            ))}
            <div className="border-t border-gray-200 mt-3 pt-3 flex justify-between items-center">
              <span className="font-medium text-gray-700">Total</span>
              <span className="font-bold text-accent">${orderDetails.total}</span>
            </div>
          </div>
        </motion.div>

        <div className="bg-gray-50/50 rounded-xl p-6 mb-6 relative">
          {/* Progress Line Background */}
          <div className="absolute top-[30%] left-0 right-0 h-1.5 bg-gray-200/70 rounded-full transform -translate-y-1/2 mx-16 sm:mx-20"></div>

          {/* Progress Line Filled */}
          <motion.div 
            initial={{ width: 0 }}
            animate={{ 
              width: orderStatus === 'Preparing' ? '0%' :
                     orderStatus === 'Out for Delivery' ? '50%' : '70%'
            }}
            transition={{ duration: 1, ease: "easeInOut" }}
            className="absolute top-[30%] left-0 h-1.5 bg-gradient-to-r from-highlight-chip via-primary-accent to-green-500 rounded-full transform -translate-y-1/2 mx-16 sm:mx-20 shadow-sm"
          ></motion.div>

          <div className="flex justify-between items-start relative z-10">
            {/* Preparing Status */}
            <motion.div 
              className="flex flex-col items-center"
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <motion.div 
                whileHover={{ scale: 1.1, rotate: 10 }}
                className={`w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mb-3 shadow-lg transition-all duration-300 
                  ${orderStatus === 'Preparing' ? 'bg-highlight-chip text-white ring-4 ring-highlight-chip/30' : 'bg-white text-gray-400'}`}
              >
                <FaUtensils className="text-2xl sm:text-3xl" />
              </motion.div>
              <span className={`text-sm sm:text-base font-medium ${orderStatus === 'Preparing' ? 'text-highlight-chip' : 'text-gray-500'}`}>Preparing</span>
              {orderStatus === 'Preparing' && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-2 px-3 py-1 bg-highlight-chip/10 rounded-full">
                  <span className="text-xs text-highlight-chip">In Progress</span>
                </motion.div>
              )}
            </motion.div>

            {/* Ready Status */}
            <motion.div 
              className="flex flex-col items-center"
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <motion.div 
                whileHover={{ scale: 1.1, rotate: -10 }}
                className={`w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mb-3 shadow-lg transition-all duration-300 
                  ${orderStatus === 'Out for Delivery' ? 'bg-primary-accent text-white ring-4 ring-primary-accent/30' : 'bg-white text-gray-400'}`}
              >
                <FaMotorcycle className="text-2xl sm:text-3xl" />
              </motion.div>
              <span className={`text-sm sm:text-base font-medium ${orderStatus === 'Out for Delivery' ? 'text-primary-accent' : 'text-gray-500'}`}>Ready</span>
              {orderStatus === 'Out for Delivery' && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-2 px-3 py-1 bg-primary-accent/10 rounded-full">
                  <span className="text-xs text-primary-accent">On the Way</span>
                </motion.div>
              )}
            </motion.div>

            {/* Delivered Status */}
            <motion.div 
              className="flex flex-col items-center"
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <motion.div 
                whileHover={{ scale: 1.1, rotate: 10 }}
                className={`w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mb-3 shadow-lg transition-all duration-300 
                  ${orderStatus === 'Delivered' ? 'bg-green-500 text-white ring-4 ring-green-500/30' : 'bg-white text-gray-400'}`}
              >
                <FaCheckCircle className="text-2xl sm:text-3xl" />
              </motion.div>
              <span className={`text-sm sm:text-base font-medium ${orderStatus === 'Delivered' ? 'text-green-500' : 'text-gray-500'}`}>Delivered</span>
              {orderStatus === 'Delivered' && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-2 px-3 py-1 bg-green-500/10 rounded-full">
                  <span className="text-xs text-green-500">Completed</span>
                </motion.div>
              )}
            </motion.div>
          </div>
        </div>

        <div className=" mt-2">
           <motion.div
             className=" bg-accent
             "
            
           />
           <motion.button
            
             whileTap={{ scale: 0.98 }}
             onClick={() => navigate(`/tableserve/${restaurantName}/${userId}/home`)}
             className=" w-full bg-accent hover:bg-accent/95 text-white py-4 sm:py-5 rounded-xl font-bold text-base sm:text-lg shadow-lg "
           >
             <motion.span
               className=" bg-accent"
              
             />
             <span className=" flex items-center justify-center gap-2">
               <span>Back to Home</span>
             </span>
           </motion.button>
         </div>
      </motion.div>
    </div>
  );
};

export default OrderTrackingScreen;