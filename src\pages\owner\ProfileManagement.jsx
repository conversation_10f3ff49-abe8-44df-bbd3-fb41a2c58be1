import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  FaStore,
  FaUser,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaClock,
  FaCamera,
  FaSave,
  FaEdit,
  FaTimes,
  FaCheck,
  FaUtensils,
  FaDollarSign,
  FaUsers,
  FaShieldAlt
} from 'react-icons/fa';
import SingleRestaurantLayout from '../../components/owner/SingleRestaurantLayout';
import ImageUpload from '../../components/common/ImageUpload';
import ProfileOTPVerification from '../../components/common/ProfileOTPVerification';

const ProfileManagement = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('restaurant');
  const [showOTPVerification, setShowOTPVerification] = useState(false);
  const [pendingChanges, setPendingChanges] = useState(null);

  const [restaurantData, setRestaurantData] = useState({
    name: 'Bella Vista Restaurant',
    description: 'Authentic Italian cuisine with a modern twist. Fresh ingredients, traditional recipes, and exceptional service.',
    cuisine: 'Italian',
    phone: '+****************',
    email: '<EMAIL>',
    address: '123 Main Street, Downtown, NY 10001',
    website: 'www.bellavista.com',
    logo: null,
    coverImage: null,
    openingHours: {
      monday: { open: '11:00', close: '22:00', closed: false },
      tuesday: { open: '11:00', close: '22:00', closed: false },
      wednesday: { open: '11:00', close: '22:00', closed: false },
      thursday: { open: '11:00', close: '22:00', closed: false },
      friday: { open: '11:00', close: '23:00', closed: false },
      saturday: { open: '10:00', close: '23:00', closed: false },
      sunday: { open: '10:00', close: '21:00', closed: false }
    },
    features: {
      delivery: true,
      takeaway: true,
      dineIn: true,
      parking: true,
      wifi: true,
      cardPayment: true,
      cashPayment: true
    }
  });

  const [ownerData, setOwnerData] = useState({
    name: 'Marco Rossi',
    email: '<EMAIL>',
    phone: '+****************',
    role: 'Owner & Head Chef',
    joinDate: '2020-03-15',
    avatar: null
  });

  const tabs = [
    { id: 'restaurant', label: 'Restaurant Info', icon: FaStore },
    { id: 'owner', label: 'Owner Profile', icon: FaUser },
    { id: 'hours', label: 'Operating Hours', icon: FaClock },
    { id: 'features', label: 'Features & Services', icon: FaUtensils }
  ];

  const handleRestaurantChange = (field, value) => {
    setRestaurantData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOwnerChange = (field, value) => {
    setOwnerData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleHoursChange = (day, field, value) => {
    setRestaurantData(prev => ({
      ...prev,
      openingHours: {
        ...prev.openingHours,
        [day]: {
          ...prev.openingHours[day],
          [field]: value
        }
      }
    }));
  };

  const handleFeatureChange = (feature, value) => {
    setRestaurantData(prev => ({
      ...prev,
      features: {
        ...prev.features,
        [feature]: value
      }
    }));
  };

  const handleSave = () => {
    // Store pending changes and show OTP verification
    setPendingChanges({ restaurantData, ownerData });
    setShowOTPVerification(true);
  };

  const handleOTPVerified = () => {
    try {
      // In a real app, this would save to backend
      console.log('Saving profile data:', pendingChanges);
      setIsEditing(false);
      setPendingChanges(null);
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please try again.');
    }
  };

  const handleOTPCancel = () => {
    setShowOTPVerification(false);
    setPendingChanges(null);
  };

  const renderRestaurantTab = () => (
    <div className="space-y-6">
      {/* Logo and Cover Image */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-primary font-raleway font-medium mb-2">Restaurant Logo</label>
          {isEditing ? (
            <ImageUpload
              currentImage={restaurantData.logo}
              onImageChange={(imageUrl) => setRestaurantData({ ...restaurantData, logo: imageUrl })}
              label="Upload restaurant logo"
              size="medium"
              shape="rounded"
            />
          ) : (
            <div className="w-32 h-32 bg-secondary rounded-lg flex items-center justify-center">
              {restaurantData.logo ? (
                <img src={restaurantData.logo} alt="Logo" className="w-full h-full object-cover rounded-lg" />
              ) : (
                <FaStore className="text-3xl text-tertiary" />
              )}
            </div>
          )}
        </div>
        <div>
          <label className="block text-primary font-raleway font-medium mb-2">Cover Image</label>
          {isEditing ? (
            <ImageUpload
              currentImage={restaurantData.coverImage}
              onImageChange={(imageUrl) => setRestaurantData({ ...restaurantData, coverImage: imageUrl })}
              label="Upload cover image"
              size="large"
              shape="rounded"
            />
          ) : (
            <div className="w-full h-32 bg-secondary rounded-lg flex items-center justify-center">
              {restaurantData.coverImage ? (
                <img src={restaurantData.coverImage} alt="Cover" className="w-full h-full object-cover rounded-lg" />
              ) : (
                <FaCamera className="text-3xl text-tertiary" />
              )}
            </div>
          )}
        </div>
      </div>

      {/* Restaurant Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Restaurant Name</label>
          <input
            type="text"
            value={restaurantData.name}
            onChange={(e) => handleRestaurantChange('name', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Cuisine Type</label>
          <input
            type="text"
            value={restaurantData.cuisine}
            onChange={(e) => handleRestaurantChange('cuisine', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Phone Number</label>
          <input
            type="tel"
            value={restaurantData.phone}
            onChange={(e) => handleRestaurantChange('phone', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Email</label>
          <input
            type="email"
            value={restaurantData.email}
            onChange={(e) => handleRestaurantChange('email', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div className="md:col-span-2">
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Address</label>
          <input
            type="text"
            value={restaurantData.address}
            onChange={(e) => handleRestaurantChange('address', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div className="md:col-span-2">
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Description</label>
          <textarea
            value={restaurantData.description}
            onChange={(e) => handleRestaurantChange('description', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none h-24 resize-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
      </div>
    </div>
  );

  const renderOwnerTab = () => (
    <div className="space-y-6">
      {/* Owner Avatar */}
      <div className="flex items-center space-x-6">
        <div className="relative">
          <div className="w-24 h-24 bg-theme-accent-primary rounded-full flex items-center justify-center">
            {ownerData.avatar ? (
              <img src={ownerData.avatar} alt="Avatar" className="w-24 h-24 rounded-full object-cover" />
            ) : (
              <FaUser className="text-3xl text-theme-text-inverse" />
            )}
          </div>
          {isEditing && (
            <button className="absolute bottom-0 right-0 w-8 h-8 bg-theme-accent-primary rounded-full flex items-center justify-center text-theme-text-inverse hover:bg-theme-accent-hover">
              <FaCamera className="text-sm" />
            </button>
          )}
        </div>
        <div>
          <h3 className="text-xl font-fredoka text-theme-text-primary">{ownerData.name}</h3>
          <p className="text-theme-text-secondary font-raleway">{ownerData.role}</p>
          <p className="text-theme-text-tertiary font-raleway text-sm">Joined {new Date(ownerData.joinDate).toLocaleDateString()}</p>
        </div>
      </div>

      {/* Owner Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Full Name</label>
          <input
            type="text"
            value={ownerData.name}
            onChange={(e) => handleOwnerChange('name', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Role</label>
          <input
            type="text"
            value={ownerData.role}
            onChange={(e) => handleOwnerChange('role', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Email</label>
          <input
            type="email"
            value={ownerData.email}
            onChange={(e) => handleOwnerChange('email', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
        <div>
          <label className="block text-theme-text-primary font-raleway font-medium mb-2">Phone</label>
          <input
            type="tel"
            value={ownerData.phone}
            onChange={(e) => handleOwnerChange('phone', e.target.value)}
            disabled={!isEditing}
            className={`w-full input-theme rounded-lg px-4 py-2 font-raleway focus:outline-none ${!isEditing ? 'opacity-60' : ''}`}
          />
        </div>
      </div>
    </div>
  );

  const renderHoursTab = () => (
    <div className="space-y-4">
      {Object.entries(restaurantData.openingHours).map(([day, hours]) => (
        <div key={day} className="flex items-center space-x-4 p-4 admin-card rounded-lg">
          <div className="w-24">
            <span className="text-theme-text-primary font-raleway font-medium capitalize">{day}</span>
          </div>
          <div className="flex items-center space-x-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={!hours.closed}
                onChange={(e) => handleHoursChange(day, 'closed', !e.target.checked)}
                disabled={!isEditing}
                className="rounded"
              />
              <span className="text-theme-text-secondary font-raleway text-sm">Open</span>
            </label>
          </div>
          {!hours.closed && (
            <>
              <input
                type="time"
                value={hours.open}
                onChange={(e) => handleHoursChange(day, 'open', e.target.value)}
                disabled={!isEditing}
                className={`input-theme rounded px-3 py-1 font-raleway ${!isEditing ? 'opacity-60' : ''}`}
              />
              <span className="text-theme-text-tertiary">to</span>
              <input
                type="time"
                value={hours.close}
                onChange={(e) => handleHoursChange(day, 'close', e.target.value)}
                disabled={!isEditing}
                className={`input-theme rounded px-3 py-1 font-raleway ${!isEditing ? 'opacity-60' : ''}`}
              />
            </>
          )}
          {hours.closed && (
            <span className="text-theme-text-tertiary font-raleway">Closed</span>
          )}
        </div>
      ))}
    </div>
  );

  const renderFeaturesTab = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {Object.entries(restaurantData.features).map(([feature, enabled]) => (
        <div key={feature} className="flex items-center justify-between p-4 admin-card rounded-lg">
          <div>
            <h3 className="text-theme-text-primary font-raleway font-medium capitalize">
              {feature.replace(/([A-Z])/g, ' $1').trim()}
            </h3>
            <p className="text-theme-text-secondary font-raleway text-sm">
              {feature === 'delivery' && 'Offer delivery service'}
              {feature === 'takeaway' && 'Allow takeaway orders'}
              {feature === 'dineIn' && 'Accept dine-in customers'}
              {feature === 'parking' && 'Parking available'}
              {feature === 'wifi' && 'Free WiFi for customers'}
              {feature === 'cardPayment' && 'Accept card payments'}
              {feature === 'cashPayment' && 'Accept cash payments'}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={enabled}
              onChange={(e) => handleFeatureChange(feature, e.target.checked)}
              disabled={!isEditing}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-theme-bg-secondary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-theme-accent-primary"></div>
          </label>
        </div>
      ))}
    </div>
  );

  return (
    <SingleRestaurantLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2">Profile Management</h1>
            <p className="text-secondary font-raleway text-sm sm:text-base">Manage your restaurant and owner profile information</p>
          </div>
          <div className="flex space-x-2">
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="btn-primary px-6 py-2 rounded-lg font-raleway flex items-center space-x-2"
              >
                <FaEdit />
                <span>Edit Profile</span>
              </button>
            ) : (
              <>
                <button
                  onClick={handleSave}
                  className="btn-primary px-6 py-2 rounded-lg font-raleway flex items-center space-x-2"
                >
                  <FaShieldAlt />
                  <span>Save Changes (OTP Required)</span>
                </button>
                <button
                  onClick={() => setIsEditing(false)}
                  className="btn-secondary px-6 py-2 rounded-lg font-raleway flex items-center space-x-2"
                >
                  <FaTimes />
                  <span>Cancel</span>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Profile Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Tabs */}
          <div className="lg:col-span-1">
            <div className="admin-card rounded-2xl p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors font-raleway ${activeTab === tab.id
                      ? 'bg-theme-accent-primary text-theme-text-inverse'
                      : 'text-theme-text-secondary hover:text-theme-text-primary hover:bg-theme-bg-hover'
                      }`}
                  >
                    <tab.icon />
                    <span>{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="admin-card rounded-2xl p-6">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                {activeTab === 'restaurant' && renderRestaurantTab()}
                {activeTab === 'owner' && renderOwnerTab()}
                {activeTab === 'hours' && renderHoursTab()}
                {activeTab === 'features' && renderFeaturesTab()}
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* OTP Verification Modal */}
      <ProfileOTPVerification
        isOpen={showOTPVerification}
        onClose={handleOTPCancel}
        onVerified={handleOTPVerified}
        phoneNumber={ownerData.phone}
        purpose="profile_update"
        entityId="restaurant_owner"
        title="Verify Profile Update"
        description="For security, please verify your phone number to save profile changes."
      />
    </SingleRestaurantLayout>
  );
};

export default ProfileManagement;
