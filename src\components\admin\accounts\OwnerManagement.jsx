import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaUser,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaDownload,
  FaPlus,
  FaStore,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaBuilding,
  FaUserTie,
  FaCrown
} from 'react-icons/fa';
import SuperAdminLayout from '../SuperAdminLayout';

const OwnerManagement = () => {
  const [owners, setOwners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock owner data
  const mockOwners = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0123',
      type: 'restaurant',
      businesses: [
        { id: 'rest-001', name: 'Bella Vista', type: 'restaurant' }
      ],
      totalBusinesses: 1,
      joinDate: '2023-06-15',
      status: 'active',
      totalRevenue: 45250.75,
      lastLogin: '2024-01-20T10:30:00Z',
      address: 'New York, NY',
      verificationStatus: 'verified'
    },
    {
      id: 2,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '******-0124',
      type: 'zone',
      businesses: [
        { id: 'zone-001', name: 'Downtown Food Street', type: 'zone' }
      ],
      totalBusinesses: 1,
      joinDate: '2023-08-22',
      status: 'active',
      totalRevenue: 125800.50,
      lastLogin: '2024-01-19T15:45:00Z',
      address: 'Los Angeles, CA',
      verificationStatus: 'verified'
    },
    {
      id: 3,
      name: 'Mike Wilson',
      email: '<EMAIL>',
      phone: '******-0125',
      type: 'shop',
      businesses: [
        { id: 'shop-001', name: 'Pizza Corner', type: 'shop', zone: 'Downtown Food Street' },
        { id: 'shop-002', name: 'Burger Barn', type: 'shop', zone: 'Riverside Food Court' }
      ],
      totalBusinesses: 2,
      joinDate: '2023-11-10',
      status: 'active',
      totalRevenue: 28750.25,
      lastLogin: '2024-01-18T09:20:00Z',
      address: 'Chicago, IL',
      verificationStatus: 'pending'
    },
    {
      id: 4,
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '******-0126',
      type: 'restaurant',
      businesses: [
        { id: 'rest-002', name: 'Spice Garden', type: 'restaurant' },
        { id: 'rest-003', name: 'Ocean Breeze', type: 'restaurant' }
      ],
      totalBusinesses: 2,
      joinDate: '2023-05-08',
      status: 'inactive',
      totalRevenue: 67890.00,
      lastLogin: '2024-01-10T14:15:00Z',
      address: 'Miami, FL',
      verificationStatus: 'verified'
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setOwners(mockOwners);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredOwners = owners.filter(owner => {
    const matchesSearch = owner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      owner.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      owner.phone.includes(searchTerm);
    const matchesType = typeFilter === 'all' || owner.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || owner.status === statusFilter;
    return matchesSearch && matchesType && matchesStatus;
  });

  const getTypeIcon = (type) => {
    switch (type) {
      case 'restaurant': return <FaStore className="text-blue-400" />;
      case 'zone': return <FaBuilding className="text-purple-400" />;
      case 'shop': return <FaUserTie className="text-green-400" />;
      default: return <FaUser className="text-gray-400" />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'restaurant': return 'text-blue-400 bg-blue-500/20';
      case 'zone': return 'text-purple-400 bg-purple-500/20';
      case 'shop': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20';
      case 'inactive': return 'text-red-400 bg-red-500/20';
      case 'suspended': return 'text-yellow-400 bg-yellow-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getVerificationColor = (status) => {
    switch (status) {
      case 'verified': return 'text-green-400 bg-green-500/20';
      case 'pending': return 'text-yellow-400 bg-yellow-500/20';
      case 'rejected': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  if (loading) {
    return (
      <SuperAdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-theme-text-secondary text-xl">Loading owners...</div>
        </div>
      </SuperAdminLayout>
    );
  }

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-secondary mb-2">Owner Management</h1>
            <p className="text-theme-text-secondary font-raleway">Manage business owners across all platforms</p>
          </div>
          <button className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded-lg font-raleway font-semibold flex items-center justify-center space-x-2">
            <FaPlus />
            <span>Add Owner</span>
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FaUser className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">{owners.length}</h3>
            <p className="text-white/70 font-raleway">Total Owners</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <FaStore className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">{owners.filter(o => o.status === 'active').length}</h3>
            <p className="text-white/70 font-raleway">Active Owners</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                <FaBuilding className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">{owners.reduce((sum, o) => sum + o.totalBusinesses, 0)}</h3>
            <p className="text-white/70 font-raleway">Total Businesses</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-accent rounded-xl flex items-center justify-center">
                <FaCrown className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">{owners.filter(o => o.verificationStatus === 'verified').length}</h3>
            <p className="text-white/70 font-raleway">Verified Owners</p>
          </motion.div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-white/10">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50" />
              <input
                type="text"
                placeholder="Search owners by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/50 focus:outline-none focus:border-accent"
              />
            </div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
            >
              <option value="all">All Types</option>
              <option value="restaurant">Restaurant Owners</option>
              <option value="zone">Zone Owners</option>
              <option value="shop">Shop Owners</option>
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>
            <button className="bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg px-4 py-2 text-white flex items-center space-x-2">
              <FaDownload />
              <span>Export</span>
            </button>
          </div>
        </div>

        {/* Owners Table */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Owner</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Type</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Businesses</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Revenue</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Status</th>
                  <th className="text-left p-4 text-white font-raleway font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredOwners.map((owner) => (
                  <motion.tr
                    key={owner.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="border-t border-white/10 hover:bg-white/5 transition-colors"
                  >
                    <td className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-accent/20 rounded-full flex items-center justify-center">
                          <FaUser className="text-accent" />
                        </div>
                        <div>
                          <h4 className="text-white font-raleway font-medium">{owner.name}</h4>
                          <p className="text-white/60 font-raleway text-sm">{owner.email}</p>
                          <p className="text-white/60 font-raleway text-sm">{owner.phone}</p>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(owner.type)}
                        <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getTypeColor(owner.type)}`}>
                          {owner.type}
                        </span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-white font-raleway">{owner.totalBusinesses} businesses</p>
                        <div className="space-y-1 mt-1">
                          {owner.businesses.slice(0, 2).map((business, index) => (
                            <p key={index} className="text-white/60 font-raleway text-sm">
                              {business.name}
                              {business.zone && <span className="text-white/40"> ({business.zone})</span>}
                            </p>
                          ))}
                          {owner.businesses.length > 2 && (
                            <p className="text-white/40 font-raleway text-xs">
                              +{owner.businesses.length - 2} more
                            </p>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <p className="text-white font-raleway">${owner.totalRevenue.toLocaleString()}</p>
                      <p className="text-white/60 font-raleway text-sm">Total Revenue</p>
                    </td>
                    <td className="p-4">
                      <div className="space-y-2">
                        <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getStatusColor(owner.status)}`}>
                          {owner.status}
                        </span>
                        <br />
                        <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getVerificationColor(owner.verificationStatus)}`}>
                          {owner.verificationStatus}
                        </span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-green-400 hover:text-green-300 hover:bg-green-500/20 rounded-lg transition-colors">
                          <FaEye />
                        </button>
                        <button className="p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-lg transition-colors">
                          <FaEdit />
                        </button>
                        <button className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-colors">
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </SuperAdminLayout>
  );
};

export default OwnerManagement;
