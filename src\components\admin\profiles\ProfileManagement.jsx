import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaStore,
  FaMapMarkerAlt,
  FaBuilding,
  FaEdit,
  FaSearch,
  FaSave,
  FaTimes,
  FaPhone,
  FaEnvelope,
  FaGlobe,
  FaClock,
  FaTag,
  FaPalette
} from 'react-icons/fa';
import SuperAdminLayout from '../SuperAdminLayout';
import ImageUpload from '../../common/ImageUpload';

const ProfileManagement = () => {
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProfile, setEditingProfile] = useState(null);

  // Mock profile data
  const mockProfiles = [
    {
      id: 1,
      type: 'restaurant',
      name: 'Bella Vista',
      slug: 'bella-vista',
      logo: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=200',
      coverImage: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800',
      description: 'Authentic Italian cuisine with a modern twist, serving traditional recipes passed down through generations.',
      category: 'Italian',
      subcategories: ['Pizza', 'Pasta', 'Wine'],
      contact: {
        phone: '+****************',
        email: '<EMAIL>',
        website: 'https://bellavista.com'
      },
      address: {
        street: '123 Main Street',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA'
      },
      hours: {
        monday: '11:00 AM - 10:00 PM',
        tuesday: '11:00 AM - 10:00 PM',
        wednesday: '11:00 AM - 10:00 PM',
        thursday: '11:00 AM - 10:00 PM',
        friday: '11:00 AM - 11:00 PM',
        saturday: '11:00 AM - 11:00 PM',
        sunday: '12:00 PM - 9:00 PM'
      },
      branding: {
        primaryColor: '#8B4513',
        secondaryColor: '#DAA520',
        accentColor: '#FF6B00'
      },
      socialMedia: {
        facebook: 'https://facebook.com/bellavista',
        instagram: 'https://instagram.com/bellavista',
        twitter: 'https://twitter.com/bellavista'
      },
      status: 'active',
      lastUpdated: '2024-01-20T10:30:00Z'
    },
    {
      id: 2,
      type: 'zone',
      name: 'Downtown Food Zone',
      slug: 'downtown-food-zone',
      logo: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=200',
      coverImage: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800',
      description: 'A vibrant food zone featuring multiple vendors offering diverse cuisines from around the world.',
      category: 'Food Court',
      subcategories: ['Asian', 'Mexican', 'American', 'Mediterranean'],
      contact: {
        phone: '+****************',
        email: '<EMAIL>',
        website: 'https://downtownfoodzone.com'
      },
      address: {
        street: '456 Downtown Plaza',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      hours: {
        monday: '10:00 AM - 10:00 PM',
        tuesday: '10:00 AM - 10:00 PM',
        wednesday: '10:00 AM - 10:00 PM',
        thursday: '10:00 AM - 10:00 PM',
        friday: '10:00 AM - 11:00 PM',
        saturday: '10:00 AM - 11:00 PM',
        sunday: '11:00 AM - 9:00 PM'
      },
      branding: {
        primaryColor: '#2E8B57',
        secondaryColor: '#FFD700',
        accentColor: '#FF6B00'
      },
      socialMedia: {
        facebook: 'https://facebook.com/downtownfoodzone',
        instagram: 'https://instagram.com/downtownfoodzone',
        twitter: 'https://twitter.com/downtownfoodzone'
      },
      status: 'active',
      lastUpdated: '2024-01-19T14:20:00Z'
    }
  ];

  useEffect(() => {
    // Load profiles
    setProfiles(mockProfiles);
    setLoading(false);
  }, []);

  const filteredProfiles = profiles.filter(profile => {
    const matchesSearch = profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || profile.type === typeFilter;
    return matchesSearch && matchesType;
  });

  const handleEditProfile = (profile) => {
    setEditingProfile({ ...profile });
    setShowEditModal(true);
  };

  const handleSaveProfile = () => {
    if (editingProfile) {
      const updatedProfiles = profiles.map(p =>
        p.id === editingProfile.id
          ? { ...editingProfile, lastUpdated: new Date().toISOString() }
          : p
      );
      setProfiles(updatedProfiles);
      setShowEditModal(false);
      setEditingProfile(null);
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'restaurant': return <FaStore className="text-blue-400" />;
      case 'zone': return <FaMapMarkerAlt className="text-purple-400" />;
      case 'shop': return <FaBuilding className="text-green-400" />;
      default: return <FaStore className="text-gray-400" />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'restaurant': return 'bg-blue-500/20 text-blue-400';
      case 'zone': return 'bg-purple-500/20 text-purple-400';
      case 'shop': return 'bg-green-500/20 text-green-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  if (loading) {
    return (
      <SuperAdminLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-secondary font-raleway">Loading profiles...</p>
          </div>
        </div>
      </SuperAdminLayout>
    );
  }

  return (
    <SuperAdminLayout>
      <div className="space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-secondary mb-2">Profile Management</h1>
            <p className="text-theme-text-secondary font-raleway text-sm sm:text-base">Manage branding and profiles for all entities</p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-secondary shadow-md">
          <div className="flex flex-col  sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="relative flex-1 ">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-text-secondary" />
              <input
                type="text"
                placeholder="Search profiles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-1/2  bg-white/10 border border-secondary rounded-lg pl-10 pr-4 py-2 text-theme-text-secondary placeholder-black focus:outline-none focus:border-accent"
              />
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="w-1/2 pr-2 bg-white/10 border border-secondary rounded-lg px-4 py-2.5 p-6 text-theme-text-secondary focus:outline-none focus:border-accent"
              >
                <option value="all" className='bg-black/60'>All Types</option>
                <option value="restaurant" className='bg-black/60'>Restaurants</option>
                <option value="zone" className='bg-black/60'>Food Zones</option>
                <option value="shop" className='bg-black/60'>Shops</option>
              </select>
            </div>

          </div>
        </div>

        {/* Profile Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 border-secondary">
          <AnimatePresence>
            {filteredProfiles.map((profile) => (
              <motion.div
                key={profile.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden border border-secondary shadow-md hover:border-accent/30 transition-all duration-300"
              >
                {/* Cover Image */}
                <div className="relative h-32 sm:h-40 bg-gradient-to-r from-gray-800 to-gray-900">
                  <img
                    src={profile.coverImage}
                    alt={profile.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 right-4">
                    <div className={`px-3 py-1 rounded-full text-xs font-raleway ${getTypeColor(profile.type)}`}>
                      {profile.type.charAt(0).toUpperCase() + profile.type.slice(1)}
                    </div>
                  </div>
                </div>

                {/* Profile Content */}
                <div className="p-4 sm:p-6">
                  {/* Logo and Name */}
                  <div className="flex items-center space-x-4 mb-4">
                    <img
                      src={profile.logo}
                      alt={profile.name}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="text-lg font-fredoka text-theme-text-secondary">{profile.name}</h3>
                      <p className="text-theme-text-secondary font-raleway text-sm">{profile.category}</p>
                    </div>
                    {getTypeIcon(profile.type)}
                  </div>

                  {/* Description */}
                  <p className="text-theme-text-secondary font-raleway text-sm mb-4 line-clamp-2">
                    {profile.description}
                  </p>

                  {/* Contact Info */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center space-x-2 text-theme-text-secondary text-sm">
                      <FaPhone className="text-accent" />
                      <span className="font-raleway">{profile.contact.phone}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-theme-text-secondary text-sm">
                      <FaEnvelope className="text-accent" />
                      <span className="font-raleway">{profile.contact.email}</span>
                    </div>
                  </div>

                  {/* Branding Colors */}


                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t border-secondary">
                    <span className="text-theme-text-secondary font-raleway text-xs">
                      Updated {new Date(profile.lastUpdated).toLocaleDateString()}
                    </span>
                    <div className="flex items-center space-x-2">

                      <button
                        onClick={() => handleEditProfile(profile)}
                        className="text-accent hover:text-accent/80 p-2 rounded-lg hover:bg-accent/20 transition-colors"
                        title="Edit Profile"
                      >
                        <FaEdit />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {filteredProfiles.length === 0 && (
          <div className="text-center py-12">
            <FaStore className="text-4xl text-theme-text-secondary mx-auto mb-4" />
            <p className="text-theme-text-secondary font-raleway">No profiles found matching your criteria</p>
          </div>
        )}
      </div>

      {/* Edit Profile Modal */}
      <AnimatePresence>
        {showEditModal && editingProfile && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="bg-black/90 backdrop-blur-xl rounded-2xl border border-accent/20 p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-fredoka text-white">Edit Profile - {editingProfile.name}</h2>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-white/60 hover:text-white text-2xl"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-fredoka text-white mb-4">Basic Information</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-white font-raleway mb-2">Name</label>
                        <input
                          type="text"
                          value={editingProfile.name}
                          onChange={(e) => setEditingProfile({ ...editingProfile, name: e.target.value })}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-raleway mb-2">Description</label>
                        <textarea
                          value={editingProfile.description}
                          onChange={(e) => setEditingProfile({ ...editingProfile, description: e.target.value })}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent h-24 resize-none"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-raleway mb-2">Category</label>
                        <input
                          type="text"
                          value={editingProfile.category}
                          onChange={(e) => setEditingProfile({ ...editingProfile, category: e.target.value })}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Branding */}
                  <div>
                    <h3 className="text-lg font-fredoka text-white mb-4">Branding</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-white font-raleway mb-2">Logo</label>
                        <ImageUpload
                          currentImage={editingProfile.logo}
                          onImageChange={(imageUrl) => setEditingProfile({ ...editingProfile, logo: imageUrl })}
                          label="Upload logo"
                          size="medium"
                          shape="rounded"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-raleway mb-2">Cover Image</label>
                        <ImageUpload
                          currentImage={editingProfile.coverImage}
                          onImageChange={(imageUrl) => setEditingProfile({ ...editingProfile, coverImage: imageUrl })}
                          label="Upload cover image"
                          size="large"
                          shape="rounded"
                        />
                      </div>
                    </div>
                  </div>

                </div>

                {/* Contact & Address */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-fredoka text-white mb-4">Contact Information</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-white font-raleway mb-2">Phone</label>
                        <input
                          type="tel"
                          value={editingProfile.contact.phone}
                          onChange={(e) => setEditingProfile({
                            ...editingProfile,
                            contact: { ...editingProfile.contact, phone: e.target.value }
                          })}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-raleway mb-2">Email</label>
                        <input
                          type="email"
                          value={editingProfile.contact.email}
                          onChange={(e) => setEditingProfile({
                            ...editingProfile,
                            contact: { ...editingProfile.contact, email: e.target.value }
                          })}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-raleway mb-2">Website</label>
                        <input
                          type="url"
                          value={editingProfile.contact.website}
                          onChange={(e) => setEditingProfile({
                            ...editingProfile,
                            contact: { ...editingProfile.contact, website: e.target.value }
                          })}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Address */}
                  <div>
                    <h3 className="text-lg font-fredoka text-white mb-4">Address</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-white font-raleway mb-2">Street</label>
                        <input
                          type="text"
                          value={editingProfile.address.street}
                          onChange={(e) => setEditingProfile({
                            ...editingProfile,
                            address: { ...editingProfile.address, street: e.target.value }
                          })}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-white font-raleway mb-2">City</label>
                          <input
                            type="text"
                            value={editingProfile.address.city}
                            onChange={(e) => setEditingProfile({
                              ...editingProfile,
                              address: { ...editingProfile.address, city: e.target.value }
                            })}
                            className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                          />
                        </div>
                        <div>
                          <label className="block text-white font-raleway mb-2">State</label>
                          <input
                            type="text"
                            value={editingProfile.address.state}
                            onChange={(e) => setEditingProfile({
                              ...editingProfile,
                              address: { ...editingProfile.address, state: e.target.value }
                            })}
                            className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-white/10 mt-6">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="px-6 py-2 text-white/70 hover:text-white font-raleway transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveProfile}
                  className="bg-accent hover:bg-accent/90 text-white px-6 py-2 rounded-lg font-raleway font-semibold flex items-center space-x-2"
                >
                  <FaSave />
                  <span>Save Changes</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </SuperAdminLayout>
  );
};

export default ProfileManagement;
