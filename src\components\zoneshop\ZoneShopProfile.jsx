import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaStore,
  FaEdit,
  FaSave,
  FaTimes,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaClock,
  FaImage,
  FaUser,
  FaTag,
  FaDollarSign,
  FaStar
} from 'react-icons/fa';
import ZoneShopLayout from './ZoneShopLayout';
import ImageUpload from '../common/ImageUpload';

const ZoneShopProfile = () => {
  const [shopData, setShopData] = useState({});
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({});

  // Mock shop data
  const mockShopData = {
    id: 'shop-001',
    name: 'Pizza Corner',
    description: 'Authentic Italian pizzas made with fresh ingredients and traditional recipes.',
    logo: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=300',
    coverImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800',
    ownerName: '<PERSON> <PERSON>',
    ownerPhone: '******-0123',
    ownerEmail: '<EMAIL>',
    cuisine: 'Italian',
    zone: {
      id: 'zone-001',
      name: 'Downtown Food Street',
      address: '123 Main Street, Downtown'
    },
    operatingHours: {
      monday: { open: '11:00', close: '22:00', closed: false },
      tuesday: { open: '11:00', close: '22:00', closed: false },
      wednesday: { open: '11:00', close: '22:00', closed: false },
      thursday: { open: '11:00', close: '22:00', closed: false },
      friday: { open: '11:00', close: '23:00', closed: false },
      saturday: { open: '11:00', close: '23:00', closed: false },
      sunday: { open: '12:00', close: '21:00', closed: false }
    },
    status: 'active',
    rating: 4.8,
    totalOrders: 1250,
    joinDate: '2023-06-15',
    specialties: ['Wood-fired Pizza', 'Fresh Pasta', 'Italian Desserts'],
    paymentMethods: ['Cash', 'Card', 'UPI', 'Digital Wallet']
  };

  useEffect(() => {
    setTimeout(() => {
      setShopData(mockShopData);
      setFormData(mockShopData);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSave = () => {
    setShopData(formData);
    setEditing(false);
    // Here you would typically save to backend
    console.log('Saving shop data:', formData);
  };

  const handleCancel = () => {
    setFormData(shopData);
    setEditing(false);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOperatingHoursChange = (day, field, value) => {
    setFormData(prev => ({
      ...prev,
      operatingHours: {
        ...prev.operatingHours,
        [day]: {
          ...prev.operatingHours[day],
          [field]: value
        }
      }
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'status-success bg-status-success-light';
      case 'inactive': return 'status-error bg-status-error-light';
      case 'pending': return 'status-warning bg-status-warning-light';
      default: return 'text-theme-text-tertiary bg-theme-bg-secondary';
    }
  };

  if (loading) {
    return (
      <ZoneShopLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-theme-text-primary text-xl">Loading shop profile...</div>
        </div>
      </ZoneShopLayout>
    );
  }

  return (
    <ZoneShopLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-white mb-2">Shop Profile</h1>
            <p className="text-white/70 font-raleway">Manage your shop information and settings</p>
          </div>
          <div className="flex space-x-4">
            {editing ? (
              <>
                <button
                  onClick={handleCancel}
                  className="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg font-raleway flex items-center space-x-2"
                >
                  <FaTimes />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={handleSave}
                  className="bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded-lg font-raleway flex items-center space-x-2"
                >
                  <FaSave />
                  <span>Save Changes</span>
                </button>
              </>
            ) : (
              <button
                onClick={() => setEditing(true)}
                className="bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded-lg font-raleway flex items-center space-x-2"
              >
                <FaEdit />
                <span>Edit Profile</span>
              </button>
            )}
          </div>
        </div>

        {/* Cover Image */}
        <div className="relative h-48 sm:h-64 bg-white/10 rounded-2xl overflow-hidden">
          {editing ? (
            <ImageUpload
              currentImage={formData.coverImage}
              onImageChange={(imageUrl) => handleInputChange('coverImage', imageUrl)}
              label=""
              size="large"
              shape="rounded"
              className="h-full"
            />
          ) : (
            <img
              src={shopData.coverImage}
              alt="Shop Cover"
              className="w-full h-full object-cover"
            />
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="absolute bottom-6 left-6 flex items-end space-x-4">
            <div className="relative">
              {editing ? (
                <ImageUpload
                  currentImage={formData.logo}
                  onImageChange={(imageUrl) => handleInputChange('logo', imageUrl)}
                  label=""
                  size="large"
                  shape="circle"
                />
              ) : (
                <img
                  src={shopData.logo}
                  alt="Shop Logo"
                  className="w-20 h-20 rounded-full border-4 border-white object-cover"
                />
              )}
            </div>
            <div>
              <h2 className="text-2xl font-fredoka text-white">{shopData.name}</h2>
              <p className="text-white/80 font-raleway">{shopData.zone.name}</p>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`px-3 py-1 rounded-full text-xs font-raleway ${getStatusColor(shopData.status)}`}>
                  {shopData.status}
                </span>
                <div className="flex items-center space-x-1">
                  <FaStar className="text-yellow-400" />
                  <span className="text-white font-raleway">{shopData.rating}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/10">
            <div className="flex items-center space-x-3">
              <FaStore className="text-accent text-xl" />
              <div>
                <h3 className="text-white font-fredoka">Active</h3>
                <p className="text-white/70 font-raleway text-sm">Shop Status</p>
              </div>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/10">
            <div className="flex items-center space-x-3">
              <FaDollarSign className="text-green-400 text-xl" />
              <div>
                <h3 className="text-white font-fredoka">{shopData.totalOrders}</h3>
                <p className="text-white/70 font-raleway text-sm">Total Orders</p>
              </div>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/10">
            <div className="flex items-center space-x-3">
              <FaStar className="text-yellow-400 text-xl" />
              <div>
                <h3 className="text-white font-fredoka">{shopData.rating}</h3>
                <p className="text-white/70 font-raleway text-sm">Average Rating</p>
              </div>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/10">
            <div className="flex items-center space-x-3">
              <FaClock className="text-blue-400 text-xl" />
              <div>
                <h3 className="text-white font-fredoka">
                  {new Date(shopData.joinDate).getFullYear()}
                </h3>
                <p className="text-white/70 font-raleway text-sm">Member Since</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
            <h3 className="text-xl font-fredoka text-white mb-6">Basic Information</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-white font-raleway mb-2">Shop Name</label>
                {editing ? (
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
                  />
                ) : (
                  <p className="text-white/80 font-raleway">{shopData.name}</p>
                )}
              </div>

              <div>
                <label className="block text-white font-raleway mb-2">Description</label>
                {editing ? (
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
                    rows="3"
                  />
                ) : (
                  <p className="text-white/80 font-raleway">{shopData.description}</p>
                )}
              </div>

              <div>
                <label className="block text-white font-raleway mb-2">Cuisine Type</label>
                {editing ? (
                  <input
                    type="text"
                    value={formData.cuisine}
                    onChange={(e) => handleInputChange('cuisine', e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
                  />
                ) : (
                  <p className="text-white/80 font-raleway">{shopData.cuisine}</p>
                )}
              </div>

              <div>
                <label className="block text-white font-raleway mb-2">Zone Location</label>
                <div className="flex items-center space-x-2">
                  <FaMapMarkerAlt className="text-white/60" />
                  <p className="text-white/80 font-raleway">{shopData.zone.name}</p>
                </div>
                <p className="text-white/60 font-raleway text-sm mt-1">{shopData.zone.address}</p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
            <h3 className="text-xl font-fredoka text-white mb-6">Contact Information</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-white font-raleway mb-2">Owner Name</label>
                {editing ? (
                  <input
                    type="text"
                    value={formData.ownerName}
                    onChange={(e) => handleInputChange('ownerName', e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
                  />
                ) : (
                  <div className="flex items-center space-x-2">
                    <FaUser className="text-white/60" />
                    <p className="text-white/80 font-raleway">{shopData.ownerName}</p>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-white font-raleway mb-2">Phone Number</label>
                {editing ? (
                  <input
                    type="tel"
                    value={formData.ownerPhone}
                    onChange={(e) => handleInputChange('ownerPhone', e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
                  />
                ) : (
                  <div className="flex items-center space-x-2">
                    <FaPhone className="text-white/60" />
                    <p className="text-white/80 font-raleway">{shopData.ownerPhone}</p>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-white font-raleway mb-2">Email Address</label>
                {editing ? (
                  <input
                    type="email"
                    value={formData.ownerEmail}
                    onChange={(e) => handleInputChange('ownerEmail', e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-accent"
                  />
                ) : (
                  <div className="flex items-center space-x-2">
                    <FaEnvelope className="text-white/60" />
                    <p className="text-white/80 font-raleway">{shopData.ownerEmail}</p>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-white font-raleway mb-2">Specialties</label>
                <div className="flex flex-wrap gap-2">
                  {shopData.specialties.map((specialty, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-accent/20 text-accent rounded-full text-sm font-raleway"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Operating Hours */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
          <h3 className="text-xl font-fredoka text-white mb-6">Operating Hours</h3>

          <div className="space-y-4">
            {Object.entries(shopData.operatingHours).map(([day, hours]) => (
              <div key={day} className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-white font-raleway capitalize w-20">{day}</span>
                  {editing ? (
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={!hours.closed}
                        onChange={(e) => handleOperatingHoursChange(day, 'closed', !e.target.checked)}
                        className="w-4 h-4 text-accent bg-white/10 border-white/20 rounded focus:ring-accent"
                      />
                      <span className="text-white/70 font-raleway text-sm">Open</span>
                    </label>
                  ) : null}
                </div>

                {hours.closed ? (
                  <span className="text-red-400 font-raleway">Closed</span>
                ) : editing ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="time"
                      value={hours.open}
                      onChange={(e) => handleOperatingHoursChange(day, 'open', e.target.value)}
                      className="bg-white/10 border border-white/20 rounded px-3 py-1 text-white text-sm focus:outline-none focus:border-accent"
                    />
                    <span className="text-white/60">to</span>
                    <input
                      type="time"
                      value={hours.close}
                      onChange={(e) => handleOperatingHoursChange(day, 'close', e.target.value)}
                      className="bg-white/10 border border-white/20 rounded px-3 py-1 text-white text-sm focus:outline-none focus:border-accent"
                    />
                  </div>
                ) : (
                  <span className="text-white/80 font-raleway">
                    {hours.open} - {hours.close}
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Payment Methods */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
          <h3 className="text-xl font-fredoka text-white mb-6">Payment Methods</h3>

          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {shopData.paymentMethods.map((method, index) => (
              <div key={index} className="bg-white/5 rounded-lg p-4 text-center">
                <div className="w-12 h-12 bg-accent/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <FaDollarSign className="text-accent" />
                </div>
                <p className="text-white font-raleway text-sm">{method}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </ZoneShopLayout>
  );
};

export default ZoneShopProfile;
