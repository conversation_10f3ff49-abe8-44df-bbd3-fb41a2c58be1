/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Legacy colors (keeping for backward compatibility)
        'primary-bg': '#121212',
        'accent': '#FF6B00',
        'highlight-chip': '#FDCB6E',
        'text-main': '#FFFFFF',
        'placeholder-subtext': '#CCCCCC',
        'card-bg': 'rgba(255,255,255,0.05)',
        'divider-border': '#2C2C2C',
        'hover-shade': '#D93217',

        // New theme-aware colors
        'theme': {
          'bg': {
            'primary': 'var(--bg-primary)',
            'secondary': 'var(--bg-secondary)',
            'tertiary': 'var(--bg-tertiary)',
            'card': 'var(--bg-card)',
            'hover': 'var(--bg-hover)',
          },
          'text': {
            'primary': 'var(--text-primary)',
            'secondary': 'var(--text-secondary)',
            'tertiary': 'var(--text-tertiary)',
            'inverse': 'var(--text-inverse)',
          },
          'border': {
            'primary': 'var(--border-primary)',
            'secondary': 'var(--border-secondary)',
            'accent': 'var(--border-accent)',
          },
          'accent': {
            'primary': 'var(--accent-primary)',
            'secondary': 'var(--accent-secondary)',
            'hover': 'var(--accent-hover)',
          }
        }
      },
      fontFamily: {
        fredoka: ['"Fredoka One"', 'cursive'],
        raleway: ['Raleway', 'sans-serif'],
      },
      keyframes: {
        shine: {
          '100%': { left: '125%' },
        },
      },
      animation: {
        shine: 'shine 1.2s ease-in-out infinite',
      },
    },
  },
  plugins: [],
}
