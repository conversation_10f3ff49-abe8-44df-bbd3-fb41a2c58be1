import LocalStorageService from './LocalStorageService';
import AuthService from './AuthService';

class DataService {
  // Get dashboard statistics for Super Admin
  static getSuperAdminStats() {
    const restaurants = LocalStorageService.getRestaurants();
    const zones = LocalStorageService.getZones();

    return {
      totalRestaurants: restaurants.length,
      activeRestaurants: restaurants.filter(r => r.status === 'active').length,
      totalZones: zones.length,
      activeZones: zones.filter(z => z.status === 'active').length,
      totalRevenue: this.calculateTotalRevenue(restaurants, zones),
      totalOrders: this.calculateTotalOrders(restaurants, zones),
      monthlyGrowth: this.calculateMonthlyGrowth(),
      activeUsers: this.calculateActiveUsers(restaurants, zones)
    };
  }

  // Get dashboard statistics for Restaurant Owner
  static getRestaurantOwnerStats(restaurantId) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    if (!restaurant) return this.getEmptyRestaurantStats();

    // Restaurant owner sees their profile data from Super Admin but everything else starts empty
    return {
      // Profile data from Super Admin
      restaurantName: restaurant.name,
      ownerName: restaurant.ownerName,
      ownerEmail: restaurant.ownerEmail,
      ownerPhone: restaurant.ownerPhone,
      address: restaurant.address,
      city: restaurant.city,
      state: restaurant.state,
      zipCode: restaurant.zipCode,
      cuisine: restaurant.cuisine,
      logo: restaurant.logo,

      // Business data - starts empty, owner creates this
      totalOrders: restaurant.orders || 0,
      totalRevenue: restaurant.revenue || '₹0',
      activeMenuItems: restaurant.menuItems?.length || 0,
      totalTables: restaurant.tables || 0,
      todayOrders: restaurant.todayOrders || 0,
      todayRevenue: restaurant.todayRevenue || '₹0',
      averageOrderValue: this.calculateAverageOrderValue(restaurant),
      customerSatisfaction: restaurant.customerSatisfaction || 0,
      popularItems: restaurant.popularItems || [],
      recentOrders: restaurant.recentOrders || [],

      // Menu and categories - owner creates these
      categories: restaurant.categories || [],
      menuItems: restaurant.menuItems || [],

      // Settings and preferences - owner manages these
      operatingHours: restaurant.operatingHours || {},
      paymentMethods: restaurant.paymentMethods || [],
      taxSettings: restaurant.taxSettings || {},

      // Full restaurant object for profile editing
      restaurantData: restaurant
    };
  }

  // Get dashboard statistics for Zone Admin
  static getZoneAdminStats(zoneId) {
    const zone = LocalStorageService.getZoneById(zoneId);
    if (!zone) return this.getEmptyZoneStats();

    // Zone admin sees their profile data from Super Admin but everything else starts empty
    return {
      // Profile data from Super Admin
      zoneName: zone.name,
      ownerName: zone.ownerName,
      ownerEmail: zone.ownerEmail,
      ownerPhone: zone.ownerPhone,
      address: zone.address,
      city: zone.city,
      state: zone.state,
      zipCode: zone.zipCode,
      description: zone.description,
      logo: zone.logo,

      // Business data - starts empty, zone admin creates this
      totalShops: zone.shops?.length || 0,
      activeShops: zone.shops?.filter(s => s.status === 'active').length || 0,
      totalOrders: zone.totalOrders || 0,
      totalRevenue: zone.totalRevenue || '₹0',
      commissionEarned: zone.commissionEarned || '₹0',
      todayOrders: zone.todayOrders || 0,
      todayRevenue: zone.todayRevenue || '₹0',
      averageOrderValue: this.calculateZoneAverageOrderValue(zone),

      // Shop management - zone admin creates and manages these
      shops: zone.shops || [],
      topPerformingShops: zone.topPerformingShops || [],
      recentOrders: zone.recentOrders || [],

      // Zone-wide settings - zone admin manages these
      operatingHours: zone.operatingHours || {},
      zoneSettings: zone.zoneSettings || {},

      // Full zone object for profile editing
      zoneData: zone
    };
  }

  // Get menu items for restaurant
  static getRestaurantMenu(restaurantId) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    return restaurant?.menuItems || [];
  }

  // Get orders for restaurant
  static getRestaurantOrders(restaurantId) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    return restaurant?.orders || [];
  }

  // Get analytics data for restaurant
  static getRestaurantAnalytics(restaurantId) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    if (!restaurant) return this.getEmptyAnalytics();

    return {
      salesData: restaurant.salesData || [],
      orderTrends: restaurant.orderTrends || [],
      popularItems: restaurant.popularItems || [],
      customerInsights: restaurant.customerInsights || {},
      revenueBreakdown: restaurant.revenueBreakdown || {},
      performanceMetrics: restaurant.performanceMetrics || {}
    };
  }

  // Get profile data for current user
  static getUserProfile() {
    const user = AuthService.getCurrentUser();
    if (!user) return null;

    if (user.role === 'restaurant_owner') {
      return LocalStorageService.getRestaurantById(user.restaurantId);
    } else if (user.role === 'zone_admin') {
      return LocalStorageService.getZoneById(user.zoneId);
    }

    return user;
  }

  // Calculate total revenue across all entities
  static calculateTotalRevenue(restaurants, zones) {
    let total = 0;

    restaurants.forEach(restaurant => {
      const revenue = restaurant.revenue || '₹0';
      const amount = parseInt(revenue.replace(/[₹,]/g, '')) || 0;
      total += amount;
    });

    zones.forEach(zone => {
      const revenue = zone.totalRevenue || '₹0';
      const amount = parseInt(revenue.replace(/[₹,]/g, '')) || 0;
      total += amount;
    });

    return `₹${total.toLocaleString()}`;
  }

  // Calculate total orders across all entities
  static calculateTotalOrders(restaurants, zones) {
    let total = 0;

    restaurants.forEach(restaurant => {
      total += restaurant.orders || 0;
    });

    zones.forEach(zone => {
      total += zone.totalOrders || 0;
    });

    return total;
  }

  // Calculate monthly growth (placeholder - would need historical data)
  static calculateMonthlyGrowth() {
    return 0; // Return 0 if no historical data
  }

  // Calculate active users
  static calculateActiveUsers(restaurants, zones) {
    return restaurants.filter(r => r.status === 'active').length +
      zones.filter(z => z.status === 'active').length;
  }

  // Calculate average order value for restaurant
  static calculateAverageOrderValue(restaurant) {
    if (!restaurant.orders || restaurant.orders === 0) return '₹0';

    const revenue = parseInt(restaurant.revenue?.replace(/[₹,]/g, '') || '0');
    const avgValue = Math.round(revenue / restaurant.orders);

    return `₹${avgValue}`;
  }

  // Calculate average order value for zone
  static calculateZoneAverageOrderValue(zone) {
    if (!zone.totalOrders || zone.totalOrders === 0) return '₹0';

    const revenue = parseInt(zone.totalRevenue?.replace(/[₹,]/g, '') || '0');
    const avgValue = Math.round(revenue / zone.totalOrders);

    return `₹${avgValue}`;
  }

  // Get empty restaurant stats
  static getEmptyRestaurantStats() {
    return {
      totalOrders: 0,
      totalRevenue: '₹0',
      activeMenuItems: 0,
      totalTables: 0,
      todayOrders: 0,
      todayRevenue: '₹0',
      averageOrderValue: '₹0',
      customerSatisfaction: 0,
      popularItems: [],
      recentOrders: []
    };
  }

  // Get empty zone stats
  static getEmptyZoneStats() {
    return {
      totalShops: 0,
      activeShops: 0,
      totalOrders: 0,
      totalRevenue: '₹0',
      commissionEarned: '₹0',
      todayOrders: 0,
      todayRevenue: '₹0',
      averageOrderValue: '₹0',
      topPerformingShops: [],
      recentOrders: []
    };
  }

  // Get empty analytics
  static getEmptyAnalytics() {
    return {
      salesData: [],
      orderTrends: [],
      popularItems: [],
      customerInsights: {},
      revenueBreakdown: {},
      performanceMetrics: {}
    };
  }

  // Update restaurant data
  static updateRestaurantData(restaurantId, data) {
    return LocalStorageService.updateRestaurant(restaurantId, data);
  }

  // Update zone data
  static updateZoneData(zoneId, data) {
    return LocalStorageService.updateZone(zoneId, data);
  }

  // Add menu item to restaurant
  static addMenuItem(restaurantId, menuItem) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    if (!restaurant) return false;

    const menuItems = restaurant.menuItems || [];
    menuItems.push({
      id: Date.now().toString(),
      ...menuItem,
      createdAt: new Date().toISOString()
    });

    return this.updateRestaurantData(restaurantId, { menuItems });
  }

  // Update menu item
  static updateMenuItem(restaurantId, menuItemId, updates) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    if (!restaurant) return false;

    const menuItems = restaurant.menuItems || [];
    const itemIndex = menuItems.findIndex(item => item.id === menuItemId);

    if (itemIndex === -1) return false;

    menuItems[itemIndex] = { ...menuItems[itemIndex], ...updates };
    return this.updateRestaurantData(restaurantId, { menuItems });
  }

  // Delete menu item
  static deleteMenuItem(restaurantId, menuItemId) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    if (!restaurant) return false;

    const menuItems = (restaurant.menuItems || []).filter(item => item.id !== menuItemId);
    return this.updateRestaurantData(restaurantId, { menuItems });
  }

  // Add order to restaurant
  static addOrder(restaurantId, order) {
    const restaurant = LocalStorageService.getRestaurantById(restaurantId);
    if (!restaurant) return false;

    const orders = restaurant.orders || [];
    const recentOrders = restaurant.recentOrders || [];

    const newOrder = {
      id: Date.now().toString(),
      ...order,
      createdAt: new Date().toISOString(),
      status: 'pending'
    };

    orders.push(newOrder);
    recentOrders.unshift(newOrder);

    // Keep only last 10 recent orders
    if (recentOrders.length > 10) {
      recentOrders.splice(10);
    }

    // Update order count and revenue
    const orderCount = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);

    return this.updateRestaurantData(restaurantId, {
      orders,
      recentOrders,
      orders: orderCount,
      revenue: `₹${totalRevenue.toLocaleString()}`
    });
  }
}

export default DataService;
