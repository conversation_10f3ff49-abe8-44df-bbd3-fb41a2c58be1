import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';
import AnalyticsService from '../../services/AnalyticsService';
import {
  FaRupeeSign,
  FaStore,
  FaUsers,
  FaChartLine,
  FaShoppingCart,
  FaStar,
  FaTable,
  FaEye,
  FaEdit,
  FaClock,
  FaMapMarkerAlt,
  FaUtensils
} from 'react-icons/fa';
import ZoneAdminLayout from './ZoneAdminLayout';

const ZoneAdminDashboard = () => {
  const { zoneId } = useParams();
  const [stats, setStats] = useState(null);
  const [vendors, setVendors] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = () => {
      setLoading(true);
      try {
        // Get analytics data from AnalyticsService
        const analyticsData = AnalyticsService.getZoneAnalytics(zoneId);

        setStats({
          todayRevenue: analyticsData.totalRevenue || 0,
          totalShops: analyticsData.totalShops || 0,
          activeShops: analyticsData.activeShops || 0,
          totalOrders: analyticsData.totalOrders || 0,
          todayOrders: analyticsData.dailyRevenue?.length > 0 ?
            analyticsData.dailyRevenue[0]?.orders || 0 : 0,
          totalRevenue: analyticsData.totalRevenue || 0
        });

        setVendors(analyticsData.topShops || []);
        setRecentOrders(analyticsData.recentOrders || []);
      } catch (error) {
        console.error('Error loading zone dashboard data:', error);
        // Set default empty data
        setStats({
          todayRevenue: 0,
          totalShops: 0,
          activeShops: 0,
          totalOrders: 0,
          todayOrders: 0,
          totalRevenue: 0
        });
        setVendors([]);
        setRecentOrders([]);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [zoneId]);

  // Real data will be loaded from useEffect

  // Real data loaded in useEffect above

  const getStatusColor = (status) => {
    switch (status) {
      case 'preparing': return 'status-warning bg-status-warning-light';
      case 'ready': return 'status-success bg-status-success-light';
      case 'completed': return 'status-info bg-status-info-light';
      case 'active': return 'status-success bg-status-success-light';
      case 'inactive': return 'status-error bg-status-error-light';
      default: return 'text-theme-text-tertiary bg-theme-bg-secondary';
    }
  };

  if (loading) {
    return (
      <ZoneAdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-theme-text-primary text-xl">Loading dashboard...</div>
        </div>
      </ZoneAdminLayout>
    );
  }

  return (
    <ZoneAdminLayout>
      <div className="w-full max-w-7xl mx-auto">
        <div className="space-y-4 lg:space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-fredoka text-theme-text-primary mb-2">
              Zone Dashboard
            </h1>
            <p className="text-sm sm:text-base text-theme-text-secondary font-raleway">
              Welcome to Downtown Food Street management panel
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="admin-card rounded-2xl p-4 sm:p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-status-success rounded-xl flex items-center justify-center">
                  <FaRupeeSign className="text-theme-text-inverse text-xl" />
                </div>
                <span className="text-status-success text-sm font-raleway">
                  {stats?.todayRevenue > 0 ? '+12.5%' : '0%'}
                </span>
              </div>
              <h3 className="text-2xl font-fredoka text-theme-text-primary mb-1">
                ₹{stats?.todayRevenue?.toLocaleString('en-IN') || '0'}
              </h3>
              <p className="text-theme-text-secondary font-raleway">Today's Revenue</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="admin-card rounded-2xl p-4 sm:p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-status-info rounded-xl flex items-center justify-center">
                  <FaStore className="text-theme-text-inverse text-xl" />
                </div>
                <span className="text-status-info text-sm font-raleway">{stats?.activeShops || 0}/{stats?.totalShops || 0}</span>
              </div>
              <h3 className="text-2xl font-fredoka text-theme-text-primary mb-1">{stats?.totalShops || 0}</h3>
              <p className="text-theme-text-secondary font-raleway">Total Shops</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="admin-card rounded-2xl p-4 sm:p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-status-warning rounded-xl flex items-center justify-center">
                  <FaShoppingCart className="text-theme-text-inverse text-xl" />
                </div>
                <span className="text-status-warning text-sm font-raleway">+{stats?.todayOrders || 0}</span>
              </div>
              <h3 className="text-2xl font-fredoka text-theme-text-primary mb-1">{stats?.totalOrders || 0}</h3>
              <p className="text-theme-text-secondary font-raleway">Total Orders</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="admin-card rounded-2xl p-4 sm:p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-theme-accent-primary rounded-xl flex items-center justify-center">
                  <FaRupeeSign className="text-theme-text-inverse text-xl" />
                </div>
                <span className="text-theme-accent-primary text-sm font-raleway">Total</span>
              </div>
              <h3 className="text-2xl font-fredoka text-theme-text-primary mb-1">
                ₹{stats?.totalRevenue?.toLocaleString('en-IN') || '0'}
              </h3>
              <p className="text-theme-text-secondary font-raleway">Total Revenue</p>
            </motion.div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Vendor Performance */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="admin-card rounded-2xl p-4 lg:p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg lg:text-xl font-fredoka text-theme-text-primary">Vendor Performance Today</h2>
                <button className="text-theme-accent-primary hover:text-theme-accent-hover font-raleway text-sm">
                  View All
                </button>
              </div>

              <div className="space-y-4">
                {vendors.map((vendor) => (
                  <div key={vendor.id} className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-accent/20 rounded-full flex items-center justify-center">
                          <FaUtensils className="text-accent" />
                        </div>
                        <div>
                          <h4 className="text-white font-raleway font-medium">{vendor.name}</h4>
                          <p className="text-white/60 font-raleway text-sm">{vendor.cuisine} • {vendor.owner}</p>
                        </div>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getStatusColor(vendor.status)}`}>
                        {vendor.status}
                      </span>
                    </div>
                    <div className="grid grid-cols-3 gap-4 mt-3">
                      <div className="text-center">
                        <p className="text-white font-fredoka">₹{vendor.todayRevenue}</p>
                        <p className="text-white/60 font-raleway text-xs">Revenue</p>
                      </div>
                      <div className="text-center">
                        <p className="text-white font-fredoka">{vendor.todayOrders}</p>
                        <p className="text-white/60 font-raleway text-xs">Orders</p>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1">
                          <FaStar className="text-yellow-400 text-sm" />
                          <p className="text-white font-fredoka">{vendor.rating}</p>
                        </div>
                        <p className="text-white/60 font-raleway text-xs">Rating</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Recent Orders */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="admin-card rounded-2xl p-4 lg:p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg lg:text-xl font-fredoka text-theme-text-primary">Recent Orders</h2>
                <button className="text-theme-accent-primary hover:text-theme-accent-hover font-raleway text-sm">
                  View All
                </button>
              </div>

              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="text-white font-raleway font-medium">{order.customerName}</h4>
                        <p className="text-white/60 font-raleway text-sm">{order.vendor} • Table {order.table}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-white font-fredoka">₹{order.total}</p>
                        <span className={`px-2 py-1 rounded-full text-xs font-raleway ${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-white/70 font-raleway text-sm">{order.items.join(', ')}</p>
                      <p className="text-white/60 font-raleway text-sm">{order.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="admin-card rounded-2xl p-4 lg:p-6"
          >
            <h2 className="text-lg lg:text-xl font-fredoka text-theme-text-primary mb-6">Quick Actions</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <button className="bg-theme-accent-primary hover:bg-theme-accent-hover text-theme-text-inverse p-3 lg:p-4 rounded-xl font-raleway font-semibold flex items-center space-x-3 transition-colors">
                <FaStore />
                <span className="text-sm lg:text-base">Manage Vendors</span>
              </button>
              <button className="bg-status-info hover:bg-status-info/90 text-theme-text-inverse p-3 lg:p-4 rounded-xl font-raleway font-semibold flex items-center space-x-3 transition-colors">
                <FaTable />
                <span className="text-sm lg:text-base">Table Layout</span>
              </button>
              <button className="bg-status-success hover:bg-status-success/90 text-theme-text-inverse p-3 lg:p-4 rounded-xl font-raleway font-semibold flex items-center space-x-3 transition-colors">
                <FaShoppingCart />
                <span className="text-sm lg:text-base">Live Orders</span>
              </button>
              <button className="bg-status-warning hover:bg-status-warning/90 text-theme-text-inverse p-3 lg:p-4 rounded-xl font-raleway font-semibold flex items-center space-x-3 transition-colors">
                <FaChartLine />
                <span className="text-sm lg:text-base">Analytics</span>
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </ZoneAdminLayout>
  );
};

export default ZoneAdminDashboard;
