import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaArrowLeft } from 'react-icons/fa';

const OTPLoginScreen = () => {
  const { restaurantName, userId } = useParams();
  const navigate = useNavigate();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);

  const handleGetOtp = () => {
    // Simulate OTP sending
    console.log(`Sending OTP to ${phoneNumber}`);
    setOtpSent(true);
  };

  const handleVerifyOtp = () => {
    // Simulate OTP verification
    console.log(`Verifying OTP ${otp} for ${phoneNumber}`);
    // On success, navigate to checkout
    navigate(`/tableserve/${restaurantName}/${userId}/checkout`);
  };

  return (
    <div className="min-h-screen bg-accent/90 text-text-main flex flex-col items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md bg-white rounded-2xl p-6 shadow-lg backdrop-blur-md "
      >
        <div className="flex items-center mb-6">
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={() => navigate(-1)}
            className="p-3 bg-accent rounded-full shadow-lg"
          >
            <FaArrowLeft className="text-xl text-white" />
          </motion.button>
          <h1 className="text-2xl text-accent ml-20">Login to Order</h1>
        </div>

        {!otpSent ? (
          <div className="space-y-4">
            <input
              type="tel"
              placeholder="Enter your phone number"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              className="w-full p-3 rounded-xl bg-white text-black placeholder-black/40 border border-black  focus:border-none focus:outline-none focus:ring-2 focus:ring-accent"
            />
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={handleGetOtp}
              className="w-full bg-accent text-text-main py-3 rounded-2xl font-fredoka text-lg shadow-lg
                         hover:bg-hover-shade transition-colors duration-300"
            >
              Get OTP
            </motion.button>
          </div>
        ) : (
          <div className="space-y-4">
            <input
              type="text"
              placeholder="Enter OTP"
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
              className="w-full p-3 rounded-xl bg-white text-black placeholder-placeholder-black/40 border border-black focus:outline-none focus:border-none focus:ring-2 focus:ring-accent"
            />
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={handleVerifyOtp}
              className="w-full bg-accent text-text-main py-3 rounded-2xl font-fredoka text-lg shadow-lg
                         hover:bg-hover-shade transition-colors duration-300"
            >
              Verify OTP
            </motion.button>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default OTPLoginScreen;