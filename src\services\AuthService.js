import LocalStorageService from './LocalStorageService';

class AuthService {
  // Admin credentials (hardcoded for super admin)
  static ADMIN_CREDENTIALS = [
    { username: 'admin', password: 'admin123' },
    { username: '8825549901', password: '12345' },
    { username: 'superadmin', password: 'super123' }
  ];

  // Authenticate user based on role and credentials
  static authenticateUser(credentials, role) {
    const { username, password } = credentials;

    try {
      switch (role) {
        case 'admin':
          return this.authenticateAdmin(username, password);

        case 'restaurant_owner':
          return this.authenticateRestaurantOwner(username, password);

        case 'zone_admin':
          return this.authenticateZoneAdmin(username, password);

        default:
          throw new Error('Invalid role specified');
      }
    } catch (error) {
      throw new Error(error.message || 'Authentication failed');
    }
  }

  // Authenticate Super Admin
  static authenticateAdmin(username, password) {
    const validCredentials = this.ADMIN_CREDENTIALS.find(
      cred => cred.username === username && cred.password === password
    );

    if (validCredentials) {
      const user = {
        id: 'super_admin',
        name: 'Super Admin',
        role: 'admin',
        username: username
      };

      const token = this.generateToken(user);
      this.setAuthSession(user, token);

      return { user, token, success: true };
    }

    throw new Error('Invalid admin credentials');
  }

  // Authenticate Restaurant Owner
  static authenticateRestaurantOwner(username, password) {
    const restaurants = LocalStorageService.getRestaurants();

    const restaurant = restaurants.find(r =>
      r.loginCredentials &&
      r.loginCredentials.username === username &&
      r.loginCredentials.password === password &&
      r.status === 'active'
    );

    if (restaurant) {
      const user = {
        id: restaurant.id,
        name: restaurant.ownerName,
        role: 'restaurant_owner',
        username: username,
        restaurantId: restaurant.id,
        restaurantName: restaurant.name,
        email: restaurant.ownerEmail,
        phone: restaurant.ownerPhone
      };

      const token = this.generateToken(user);
      this.setAuthSession(user, token);

      // Update last login
      LocalStorageService.updateRestaurant(restaurant.id, {
        loginCredentials: {
          ...restaurant.loginCredentials,
          lastLogin: new Date().toISOString()
        }
      });

      return { user, token, success: true };
    }

    throw new Error('Invalid restaurant credentials or account inactive');
  }

  // Authenticate Zone Admin
  static authenticateZoneAdmin(username, password) {
    const zones = LocalStorageService.getZones();

    const zone = zones.find(z =>
      z.loginCredentials &&
      z.loginCredentials.username === username &&
      z.loginCredentials.password === password &&
      z.status === 'active'
    );

    if (zone) {
      const user = {
        id: zone.id,
        name: zone.ownerName,
        role: 'zone_admin',
        username: username,
        zoneId: zone.id,
        zoneName: zone.name,
        email: zone.ownerEmail,
        phone: zone.ownerPhone
      };

      const token = this.generateToken(user);
      this.setAuthSession(user, token);

      // Update last login
      LocalStorageService.updateZone(zone.id, {
        loginCredentials: {
          ...zone.loginCredentials,
          lastLogin: new Date().toISOString()
        }
      });

      return { user, token, success: true };
    }

    throw new Error('Invalid zone credentials or account inactive');
  }

  // Try to authenticate with any role (for login form)
  static authenticateAnyRole(username, password) {
    const roles = ['admin', 'restaurant_owner', 'zone_admin'];

    for (const role of roles) {
      try {
        const result = this.authenticateUser({ username, password }, role);
        return result;
      } catch (error) {
        // Continue to next role
        continue;
      }
    }

    throw new Error('Invalid credentials');
  }

  // Generate JWT-like token (simplified for demo)
  static generateToken(user) {
    const payload = {
      id: user.id,
      role: user.role,
      username: user.username,
      iat: Date.now(),
      exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    };

    // In production, use proper JWT signing
    return btoa(JSON.stringify(payload));
  }

  // Set authentication session
  static setAuthSession(user, token) {
    localStorage.setItem('auth_token', token);
    localStorage.setItem('auth_user', JSON.stringify(user));
    localStorage.setItem('auth_timestamp', Date.now().toString());
  }

  // Get current authenticated user
  static getCurrentUser() {
    try {
      const token = localStorage.getItem('auth_token');
      const userStr = localStorage.getItem('auth_user');

      if (!token || !userStr) {
        return null;
      }

      const user = JSON.parse(userStr);

      // Verify token is not expired
      if (this.isTokenExpired(token)) {
        this.logout();
        return null;
      }

      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Check if token is expired
  static isTokenExpired(token) {
    try {
      const payload = JSON.parse(atob(token));
      return Date.now() > payload.exp;
    } catch (error) {
      return true;
    }
  }

  // Check if user is authenticated
  static isAuthenticated() {
    const user = this.getCurrentUser();
    return user !== null;
  }

  // Check if user has specific role
  static hasRole(role) {
    const user = this.getCurrentUser();
    return user && user.role === role;
  }

  // Logout user
  static logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    localStorage.removeItem('auth_timestamp');
  }

  // Get authentication redirect path based on role
  static getRedirectPath(role) {
    switch (role) {
      case 'admin':
        return '/admin/dashboard';
      case 'restaurant_owner':
        return '/owner/dashboard';
      case 'zone_admin':
        return '/zone-admin/dashboard';
      default:
        return '/login';
    }
  }

  // Validate session and refresh if needed
  static validateSession() {
    const user = this.getCurrentUser();
    if (!user) {
      return false;
    }

    // Check if user's account still exists and is active
    if (user.role === 'restaurant_owner') {
      const restaurant = LocalStorageService.getRestaurantById(user.restaurantId);
      if (!restaurant || restaurant.status !== 'active') {
        this.logout();
        return false;
      }
    } else if (user.role === 'zone_admin') {
      const zone = LocalStorageService.getZoneById(user.zoneId);
      if (!zone || zone.status !== 'active') {
        this.logout();
        return false;
      }
    }

    return true;
  }

  // Get user profile data
  static getUserProfile() {
    const user = this.getCurrentUser();
    if (!user) return null;

    if (user.role === 'restaurant_owner') {
      return LocalStorageService.getRestaurantById(user.restaurantId);
    } else if (user.role === 'zone_admin') {
      return LocalStorageService.getZoneById(user.zoneId);
    }

    return user;
  }
}

export default AuthService;
