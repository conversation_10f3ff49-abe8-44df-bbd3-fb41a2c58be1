import React, { useState, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaPlus, FaMinus, FaShoppingCart, FaArrowLeft } from 'react-icons/fa';
import Footer from '../../components/Footer';
import { CartContext } from '../../context/CartContext.jsx';

const dishes = [
  { id: 'dish1', name: 'Spicy Chicken Wings', price: 12.99, image: '/src/assets/burger.jpg', category: 'starters', description: 'Crispy chicken wings tossed in a fiery hot sauce. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.' },
  { id: 'dish2', name: 'Classic Burger', price: 15.50, image: '/src/assets/burger.jpg', category: 'mains', description: 'Juicy beef patty with lettuce, tomato, and cheese. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.' },
  { id: 'dish3', name: 'Lemonade', price: 4.00, image: '/src/assets/burger.jpg', category: 'beverages', description: 'Refreshing homemade lemonade. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.' },
  { id: 'dish4', name: 'Chocolate Lava Cake', price: 8.75, image: '/src/assets/burger.jpg', category: 'desserts', description: 'Warm chocolate cake with a molten center. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.' },
  { id: 'dish5', name: 'Vegetable Spring Rolls', price: 9.50, image: '/src/assets/burger.jpg', category: 'starters', description: 'Crispy spring rolls filled with fresh vegetables.' },
  { id: 'dish6', name: 'Grilled Salmon', price: 22.00, image: '/src/assets/burger.jpg', category: 'mains', description: 'Perfectly grilled salmon with asparagus.' },
];

const ProductDetailScreen = () => {
  const { restaurantName, userId, dishId } = useParams();
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(1);
  const { addToCart } = useContext(CartContext);

  const dish = dishes.find(d => d.id === dishId);

  if (!dish) {
    return <div className="min-h-screen bg-primary-bg text-text-main flex items-center justify-center">Dish not found!</div>;
  }

  const handleAddToCart = () => {
    // Add to cart with the specified quantity
    addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity: quantity
    });
    
    console.log(`Added ${quantity} of ${dish.name} to cart.`);
    navigate(`/tableserve/${restaurantName}/${userId}/cart`);
  };

  return (
    <>
      <div className="min-h-screen bg-white  text-primary-bg p-8">
        {/* Header */}
        <div className="flex items-center mb-6">
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={() => navigate(-1)}
            className="p-3 bg-accent rounded-full shadow-lg border "
          >
            <FaArrowLeft className="text-xl text-text-main" />
          </motion.button>
          <h1 className="text-2xl text-accent font-raleway ml-4">{dish.name}</h1>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-accent rounded-2xl shadow-2xl  shadow-black/40"
        >
          <img src={dish.image} alt={dish.name} className="w-full h-48 object-cover bg-white rounded-t-xl mb-4" />
          <div className='pl-2 text-white'>
            <div className='flex justify-between pr-4'>
              <h2 className="text-xl font-raleway text-text-main ">{dish.name}</h2>
              <p className="text-primary-accent text-xl  "> ₹{dish.price.toFixed(2)}</p>
            </div>
            <p className=" text-base">{dish.description}</p>
          </div>
          {/* Customization (e.g., spice level) */}
          <div className="mb-6 flex justify-between">
            <h3 className="text-lg pt-4 pl-2 text-white">Customization</h3>

            <select className="  m-2 p-2 mt-4 rounded-xl bg-accent text-white border border-white focus:outline-none focus:ring-0 focus:ring-white">
              <div className=' text-white bg-white border  border-white'>
                <option className='bg-white text-accent '>No Spice</option>
                <option className='bg-white text-accent '>Mild</option>
                <option className='bg-white text-accent '>Medium</option>
                <option className='bg-white text-accent' >Hot</option>
              </div>
            </select>

          </div>
          {/* Quantity Selector */}
          <div className="flex items-center bg-white w-24 ml-2 p-1 rounded-full justify-center -mt-6 ">
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
              className=" rounded-full"
            >
              <FaMinus className="text-accent text-xs" />
            </motion.button>
            <span className="text-xl font-raleway text-accent mx-6">{quantity}</span>
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={() => setQuantity(prev => prev + 1)}
              className="  rounded-full"
            >
              <FaPlus className="text-accent text-xs" />
            </motion.button>
          </div>
          <div className='pb-6'>
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={handleAddToCart}
              className="flex text-white w-48 ml-32 hover:bg-white hover:text-accent border border-white  hover:border-accent  mt-6 h-12 font-bold rounded-xl p-2 items-center bg-accent"
            >
              <FaShoppingCart className="mr-3 flex  ml-6" /> Add to Cart
            </motion.button>
          </div>
        </motion.div>
      </div >
      <Footer />
    </>
  );
};

export default ProductDetailScreen;