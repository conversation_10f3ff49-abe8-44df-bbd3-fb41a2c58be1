import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaQrcode, FaCamera, FaArrowRight } from 'react-icons/fa';

const QRDemo = () => {
  const [restaurantName, setRestaurantName] = useState('bella-vista');
  const [tableId, setTableId] = useState('table-5');
  const navigate = useNavigate();

  const handleScanDemo = () => {
    // Simulate QR code scan by navigating to the restaurant menu
    navigate(`/${restaurantName}/${tableId}/menu`);
  };

  const generateQRUrl = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/${restaurantName}/${tableId}/menu`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-md w-full"
      >
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
            <FaQrcode className="text-3xl text-white" />
          </div>
          <h1 className="text-3xl font-fredoka text-white mb-2">QR Code Demo</h1>
          <p className="text-white/70 font-raleway">
            Simulate scanning a QR code to access restaurant menu
          </p>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6">
          <h3 className="text-lg font-fredoka text-white mb-4">Configure Demo</h3>

          <div className="space-y-4">
            <div>
              <label className="block text-white/80 font-raleway mb-2">Restaurant Name</label>
              <input
                type="text"
                value={restaurantName}
                onChange={(e) => setRestaurantName(e.target.value)}
                className="w-full bg-white/5 border border-white/20 rounded-lg py-3 px-4 text-white focus:outline-none focus:border-accent"
                placeholder="bella-vista"
              />
              <p className="text-white/40 text-xs mt-1">Use lowercase with hyphens (e.g., bella-vista)</p>
            </div>

            <div>
              <label className="block text-white/80 font-raleway mb-2">Table ID</label>
              <input
                type="text"
                value={tableId}
                onChange={(e) => setTableId(e.target.value)}
                className="w-full bg-white/5 border border-white/20 rounded-lg py-3 px-4 text-white focus:outline-none focus:border-accent"
                placeholder="table-5"
              />
              <p className="text-white/40 text-xs mt-1">Use format: table-{number} (e.g., table-5)</p>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6">
          <h3 className="text-lg font-fredoka text-white mb-4">Generated QR URL</h3>
          <div className="bg-white/5 rounded-lg p-3 mb-4">
            <p className="text-white/70 font-raleway text-sm break-all">
              {generateQRUrl()}
            </p>
          </div>
          <p className="text-white/60 font-raleway text-xs">
            In a real implementation, this URL would be encoded in a QR code that customers scan with their phones.
          </p>
        </div>

        <motion.button
          onClick={handleScanDemo}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
        >
          <FaCamera />
          <span>Simulate QR Scan</span>
          <FaArrowRight />
        </motion.button>

        <div className="mt-6 text-center">
          <p className="text-white/60 font-raleway text-sm">
            This demo simulates a customer scanning a QR code at their table to access the restaurant menu.
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default QRDemo;
