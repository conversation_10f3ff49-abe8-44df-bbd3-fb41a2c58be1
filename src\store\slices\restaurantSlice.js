import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Mock data and localStorage functions
const getRestaurantsFromStorage = () => {
  const stored = localStorage.getItem('restaurants');
  return stored ? JSON.parse(stored) : [];
};

const saveRestaurantsToStorage = (restaurants) => {
  localStorage.setItem('restaurants', JSON.stringify(restaurants));
};

export const fetchRestaurants = createAsyncThunk(
  'restaurant/fetchRestaurants',
  async (_, { rejectWithValue }) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      const restaurants = getRestaurantsFromStorage();
      return restaurants;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const createRestaurant = createAsyncThunk(
  'restaurant/createRestaurant',
  async (restaurantData, { rejectWithValue }) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      const restaurants = getRestaurantsFromStorage();
      const newRestaurant = {
        id: `res-${Date.now()}`,
        ...restaurantData,
        status: 'active',
      };
      const updatedRestaurants = [...restaurants, newRestaurant];
      saveRestaurantsToStorage(updatedRestaurants);
      return newRestaurant;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const restaurantSlice = createSlice({
  name: 'restaurant',
  initialState: {
    restaurants: [],
    currentRestaurant: null,
    loading: false,
    error: null,
  },
  reducers: {
    setCurrentRestaurant: (state, action) => {
      state.currentRestaurant = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRestaurants.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchRestaurants.fulfilled, (state, action) => {
        state.restaurants = action.payload;
        state.loading = false;
      })
      .addCase(fetchRestaurants.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(createRestaurant.pending, (state) => {
        state.loading = true;
      })
      .addCase(createRestaurant.fulfilled, (state, action) => {
        state.restaurants.push(action.payload);
        state.loading = false;
      })
      .addCase(createRestaurant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setCurrentRestaurant, clearError } = restaurantSlice.actions;
export default restaurantSlice.reducer;