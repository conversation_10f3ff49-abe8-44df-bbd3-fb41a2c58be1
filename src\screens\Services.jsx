import React from 'react';
import { FaQrcode, FaChartLine, FaShieldAlt } from 'react-icons/fa';

const Services = () => (
    <div className="bg-primary-bg text-white min-h-screen">
        <div className="container mx-auto px-4 py-20">
            <div className="text-center mb-16" data-aos="fade-down">
                <h1 className="text-5xl md:text-6xl font-fredoka text-accent mb-4">Our Services</h1>
                <p className="text-lg font-raleway max-w-3xl mx-auto">
                    We provide a suite of innovative services designed to elevate the dining experience for both customers and restaurant owners. Our technology is crafted to be seamless, intuitive, and powerful.
                </p>
            </div>
            <div className="grid md:grid-cols-3 gap-12">
                <div className="bg-white/5 p-8 rounded-xl shadow-lg hover:shadow-accent/20 transform hover:-translate-y-2 transition-all duration-300" data-aos="fade-up" data-aos-delay="100">
                    <div className="flex justify-center mb-6">
                        <FaQrcode className="text-5xl text-accent" />
                    </div>
                    <h2 className="text-2xl font-fredoka mb-4 text-center">QR-Powered Ordering</h2>
                    <p className="font-raleway text-center">
                        Empower your customers with the ability to browse your menu, place orders, and pay their bills directly from their smartphones. This contactless solution reduces wait times and increases table turnover.
                    </p>
                </div>
                <div className="bg-white/5 p-8 rounded-xl shadow-lg hover:shadow-accent/20 transform hover:-translate-y-2 transition-all duration-300" data-aos="fade-up" data-aos-delay="200">
                    <div className="flex justify-center mb-6">
                        <FaChartLine className="text-5xl text-accent" />
                    </div>
                    <h2 className="text-2xl font-fredoka mb-4 text-center">Advanced Analytics</h2>
                    <p className="font-raleway text-center">
                        Make data-driven decisions with our comprehensive analytics dashboard. Track sales, monitor popular items, and understand customer preferences to optimize your menu and marketing strategies.
                    </p>
                </div>
                <div className="bg-white/5 p-8 rounded-xl shadow-lg hover:shadow-accent/20 transform hover:-translate-y-2 transition-all duration-300" data-aos="fade-up" data-aos-delay="300">
                    <div className="flex justify-center mb-6">
                        <FaShieldAlt className="text-5xl text-accent" />
                    </div>
                    <h2 className="text-2xl font-fredoka mb-4 text-center">Secure & Integrated Payments</h2>
                    <p className="font-raleway text-center">
                        Offer a variety of payment options with our secure, PCI-compliant payment gateway. From credit cards to digital wallets, we ensure every transaction is smooth and protected.
                    </p>
                </div>
            </div>
        </div>
    </div>
);

export default Services;
