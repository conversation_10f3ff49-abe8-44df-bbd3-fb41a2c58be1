import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import restaurantSlice from './slices/restaurantSlice';
import menuSlice from './slices/menuSlice';
import orderSlice from './slices/orderSlice';
import analyticsSlice from './slices/analyticsSlice';
import cartSlice from './slices/cartSlice';
import ownerSlice from './slices/ownerSlice';
import activitySlice from './slices/activitySlice';
import themeSlice from './slices/themeSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    restaurant: restaurantSlice,
    menu: menuSlice,
    order: orderSlice,
    analytics: analyticsSlice,
    cart: cartSlice,
    owner: ownerSlice,
    activity: activitySlice,
    theme: themeSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

// Remove TypeScript type aliases - not supported in JS files

