// Local Storage Service for TableServe Application
class LocalStorageService {
  // Storage keys
  static KEYS = {
    RESTAURANTS: 'tableserve_restaurants',
    ZONES: 'tableserve_zones',
    OTP_SESSIONS: 'tableserve_otp_sessions',
    ZONE_SHOPS: 'tableserve_zone_shops',
    ZONE_VENDORS: 'tableserve_zone_vendors',
    ZONE_ORDERS: 'tableserve_zone_orders',
    ZONE_ANALYTICS: 'tableserve_zone_analytics'
  };

  // Restaurant Management
  static getRestaurants() {
    try {
      const data = localStorage.getItem(this.KEYS.RESTAURANTS);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error loading restaurants from localStorage:', error);
      return [];
    }
  }

  static getRestaurant(restaurantId) {
    const restaurants = this.getRestaurants();
    return restaurants.find(r => r.id == restaurantId) || null;
  }

  static saveRestaurants(restaurants) {
    try {
      localStorage.setItem(this.KEYS.RESTAURANTS, JSON.stringify(restaurants));
      return true;
    } catch (error) {
      console.error('Error saving restaurants to localStorage:', error);
      return false;
    }
  }

  static addRestaurant(restaurant) {
    const restaurants = this.getRestaurants();
    const newRestaurant = {
      ...restaurant,
      id: Date.now().toString(), // Convert to string for consistency
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    restaurants.push(newRestaurant);
    this.saveRestaurants(restaurants);
    return newRestaurant;
  }

  static updateRestaurant(restaurantId, updates) {
    const restaurants = this.getRestaurants();
    const index = restaurants.findIndex(r => r.id == restaurantId); // Use == for type coercion
    if (index !== -1) {
      restaurants[index] = {
        ...restaurants[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      this.saveRestaurants(restaurants);
      return restaurants[index];
    }
    return null;
  }

  static deleteRestaurant(restaurantId) {
    const restaurants = this.getRestaurants();
    const filtered = restaurants.filter(r => r.id !== restaurantId);
    this.saveRestaurants(filtered);
    return filtered.length < restaurants.length;
  }

  static getRestaurantById(restaurantId) {
    const restaurants = this.getRestaurants();
    return restaurants.find(r => r.id == restaurantId) || null; // Use == for type coercion
  }

  // Zone Management
  static getZones() {
    try {
      const data = localStorage.getItem(this.KEYS.ZONES);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error loading zones from localStorage:', error);
      return [];
    }
  }

  static saveZones(zones) {
    try {
      localStorage.setItem(this.KEYS.ZONES, JSON.stringify(zones));
      return true;
    } catch (error) {
      console.error('Error saving zones to localStorage:', error);
      return false;
    }
  }

  static addZone(zone) {
    const zones = this.getZones();
    const newZone = {
      ...zone,
      id: Date.now().toString(), // Convert to string for consistency
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    zones.push(newZone);
    this.saveZones(zones);
    return newZone;
  }

  static updateZone(zoneId, updates) {
    const zones = this.getZones();
    const index = zones.findIndex(z => z.id == zoneId); // Use == for type coercion
    if (index !== -1) {
      zones[index] = {
        ...zones[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      this.saveZones(zones);
      return zones[index];
    }
    return null;
  }

  static deleteZone(zoneId) {
    const zones = this.getZones();
    const filtered = zones.filter(z => z.id !== zoneId);
    this.saveZones(filtered);
    return filtered.length < zones.length;
  }

  static getZoneById(zoneId) {
    const zones = this.getZones();
    return zones.find(z => z.id == zoneId) || null; // Use == for type coercion
  }

  // OTP Session Management
  static getOTPSessions() {
    try {
      const data = localStorage.getItem(this.KEYS.OTP_SESSIONS);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Error loading OTP sessions from localStorage:', error);
      return {};
    }
  }

  static saveOTPSessions(sessions) {
    try {
      localStorage.setItem(this.KEYS.OTP_SESSIONS, JSON.stringify(sessions));
      return true;
    } catch (error) {
      console.error('Error saving OTP sessions to localStorage:', error);
      return false;
    }
  }

  static createOTPSession(phoneNumber, otp, purpose, entityId) {
    const sessions = this.getOTPSessions();
    const sessionId = `${phoneNumber}_${Date.now()}`;
    const session = {
      id: sessionId,
      phoneNumber,
      otp,
      purpose, // 'password_reset', 'password_change'
      entityId, // restaurant or zone ID
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes
      verified: false
    };

    sessions[sessionId] = session;
    this.saveOTPSessions(sessions);
    return session;
  }

  static verifyOTP(sessionId, enteredOTP) {
    const sessions = this.getOTPSessions();
    const session = sessions[sessionId];

    if (!session) {
      return { success: false, message: 'Invalid session' };
    }

    if (new Date() > new Date(session.expiresAt)) {
      delete sessions[sessionId];
      this.saveOTPSessions(sessions);
      return { success: false, message: 'OTP expired' };
    }

    if (session.otp !== enteredOTP) {
      return { success: false, message: 'Invalid OTP' };
    }

    session.verified = true;
    sessions[sessionId] = session;
    this.saveOTPSessions(sessions);

    return { success: true, session };
  }

  static cleanupExpiredOTPSessions() {
    const sessions = this.getOTPSessions();
    const now = new Date();
    let cleaned = false;

    Object.keys(sessions).forEach(sessionId => {
      if (new Date(sessions[sessionId].expiresAt) < now) {
        delete sessions[sessionId];
        cleaned = true;
      }
    });

    if (cleaned) {
      this.saveOTPSessions(sessions);
    }
  }

  // Utility Methods
  static generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  }

  static clearAllData() {
    try {
      localStorage.removeItem(this.KEYS.RESTAURANTS);
      localStorage.removeItem(this.KEYS.ZONES);
      localStorage.removeItem(this.KEYS.OTP_SESSIONS);
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  }

  // Data Export/Import for backup
  static exportData() {
    return {
      restaurants: this.getRestaurants(),
      zones: this.getZones(),
      exportedAt: new Date().toISOString()
    };
  }

  static importData(data) {
    try {
      if (data.restaurants) {
        this.saveRestaurants(data.restaurants);
      }
      if (data.zones) {
        this.saveZones(data.zones);
      }
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // Search and Filter Utilities
  static searchRestaurants(searchTerm, filters = {}) {
    const restaurants = this.getRestaurants();
    let filtered = restaurants;

    // Apply search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(restaurant =>
        restaurant.name.toLowerCase().includes(term) ||
        restaurant.ownerName.toLowerCase().includes(term) ||
        restaurant.cuisine.toLowerCase().includes(term) ||
        restaurant.address.toLowerCase().includes(term)
      );
    }

    // Apply filters
    if (filters.status) {
      filtered = filtered.filter(restaurant => restaurant.status === filters.status);
    }

    if (filters.cuisine) {
      filtered = filtered.filter(restaurant => restaurant.cuisine === filters.cuisine);
    }

    return filtered;
  }

  static searchZones(searchTerm, filters = {}) {
    const zones = this.getZones();
    let filtered = zones;

    // Apply search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(zone =>
        zone.name.toLowerCase().includes(term) ||
        zone.ownerName.toLowerCase().includes(term) ||
        zone.city.toLowerCase().includes(term) ||
        zone.address.toLowerCase().includes(term)
      );
    }

    // Apply filters
    if (filters.status) {
      filtered = filtered.filter(zone => zone.status === filters.status);
    }

    if (filters.city) {
      filtered = filtered.filter(zone => zone.city === filters.city);
    }

    return filtered;
  }

  // Zone Shops Management
  static getZoneShops(zoneId) {
    try {
      const data = localStorage.getItem(`${this.KEYS.ZONE_SHOPS}_${zoneId}`);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error loading zone shops from localStorage:', error);
      return [];
    }
  }

  static saveZoneShops(zoneId, shops) {
    try {
      localStorage.setItem(`${this.KEYS.ZONE_SHOPS}_${zoneId}`, JSON.stringify(shops));
      return true;
    } catch (error) {
      console.error('Error saving zone shops to localStorage:', error);
      return false;
    }
  }

  static addZoneShop(zoneId, shop) {
    const shops = this.getZoneShops(zoneId);
    const newShop = {
      ...shop,
      id: Date.now().toString(),
      zoneId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      todayRevenue: 0,
      todayOrders: 0,
      totalOrders: 0,
      rating: 0,
      joinDate: new Date().toISOString().split('T')[0],
      lastLogin: new Date().toISOString()
    };
    shops.push(newShop);
    this.saveZoneShops(zoneId, shops);

    // Save shop credentials for login
    this.saveShopCredentials(newShop);

    return newShop;
  }

  static updateZoneShop(zoneId, shopId, updates) {
    const shops = this.getZoneShops(zoneId);
    const index = shops.findIndex(s => s.id == shopId);
    if (index !== -1) {
      shops[index] = {
        ...shops[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      this.saveZoneShops(zoneId, shops);
      return shops[index];
    }
    return null;
  }

  static deleteZoneShop(zoneId, shopId) {
    const shops = this.getZoneShops(zoneId);
    const filtered = shops.filter(s => s.id != shopId);
    this.saveZoneShops(zoneId, filtered);
    return filtered.length < shops.length;
  }

  // Zone Vendors Management
  static getZoneVendors(zoneId) {
    try {
      const data = localStorage.getItem(`${this.KEYS.ZONE_VENDORS}_${zoneId}`);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error loading zone vendors from localStorage:', error);
      return [];
    }
  }

  static saveZoneVendors(zoneId, vendors) {
    try {
      localStorage.setItem(`${this.KEYS.ZONE_VENDORS}_${zoneId}`, JSON.stringify(vendors));
      return true;
    } catch (error) {
      console.error('Error saving zone vendors to localStorage:', error);
      return false;
    }
  }

  static addZoneVendor(zoneId, vendor) {
    const vendors = this.getZoneVendors(zoneId);
    const newVendor = {
      ...vendor,
      id: Date.now().toString(),
      zoneId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      monthlyRevenue: 0,
      totalOrders: 0,
      rating: 0,
      menuItems: 0,
      tableCount: 0,
      avgOrderValue: 0,
      lastActivity: new Date().toISOString()
    };
    vendors.push(newVendor);
    this.saveZoneVendors(zoneId, vendors);
    return newVendor;
  }

  static updateZoneVendor(zoneId, vendorId, updates) {
    const vendors = this.getZoneVendors(zoneId);
    const index = vendors.findIndex(v => v.id == vendorId);
    if (index !== -1) {
      vendors[index] = {
        ...vendors[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      this.saveZoneVendors(zoneId, vendors);
      return vendors[index];
    }
    return null;
  }

  static deleteZoneVendor(zoneId, vendorId) {
    const vendors = this.getZoneVendors(zoneId);
    const filtered = vendors.filter(v => v.id != vendorId);
    this.saveZoneVendors(zoneId, filtered);
    return filtered.length < vendors.length;
  }

  // Zone Profile Management
  static getZoneProfile(zoneId) {
    const zones = this.getZones();
    return zones.find(z => z.id == zoneId) || null;
  }

  static updateZoneProfile(zoneId, updates) {
    return this.updateZone(zoneId, updates);
  }

  // Zone Shop Credentials Management
  static saveShopCredentials(shopData) {
    try {
      // Save shop credentials for login system
      const credentials = localStorage.getItem('tableserve_shop_credentials') || '{}';
      const credentialsData = JSON.parse(credentials);

      if (shopData.loginCredentials) {
        credentialsData[shopData.loginCredentials.username] = {
          shopId: shopData.id,
          zoneId: shopData.zoneId,
          password: shopData.loginCredentials.password,
          shopName: shopData.name,
          ownerName: shopData.ownerName,
          type: 'zone_shop',
          status: shopData.status,
          createdAt: shopData.createdAt || new Date().toISOString()
        };

        localStorage.setItem('tableserve_shop_credentials', JSON.stringify(credentialsData));
      }

      return true;
    } catch (error) {
      console.error('Error saving shop credentials:', error);
      return false;
    }
  }

  static validateShopCredentials(username, password) {
    try {
      const credentials = localStorage.getItem('tableserve_shop_credentials') || '{}';
      const credentialsData = JSON.parse(credentials);

      const shopCreds = credentialsData[username];
      if (shopCreds && shopCreds.password === password && shopCreds.status === 'active') {
        return {
          valid: true,
          shopData: shopCreds
        };
      }

      return { valid: false };
    } catch (error) {
      console.error('Error validating shop credentials:', error);
      return { valid: false };
    }
  }

  static updateShopCredentials(shopId, zoneId, newPassword) {
    try {
      const credentials = localStorage.getItem('tableserve_shop_credentials') || '{}';
      const credentialsData = JSON.parse(credentials);

      // Find and update the shop credentials
      for (const username in credentialsData) {
        if (credentialsData[username].shopId === shopId && credentialsData[username].zoneId === zoneId) {
          credentialsData[username].password = newPassword;
          credentialsData[username].updatedAt = new Date().toISOString();
          break;
        }
      }

      localStorage.setItem('tableserve_shop_credentials', JSON.stringify(credentialsData));
      return true;
    } catch (error) {
      console.error('Error updating shop credentials:', error);
      return false;
    }
  }
}

export default LocalStorageService;
