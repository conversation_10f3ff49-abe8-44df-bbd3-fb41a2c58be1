import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaQrcode, FaStore, FaMapMarkerAlt, FaTable, FaArrowRight } from 'react-icons/fa';

const QRTestPage = () => {
  const navigate = useNavigate();
  const [selectedType, setSelectedType] = useState('restaurant');

  const testQRCodes = {
    restaurant: [
      {
        name: 'Bella Vista Restaurant',
        slug: 'bella-vista',
        table: '5',
        url: '/r/bella-vista/t/5',
        description: 'Single restaurant QR code'
      },
      {
        name: 'Ocean Breeze Cafe',
        slug: 'ocean-breeze',
        table: '12',
        url: '/r/ocean-breeze/t/12',
        description: 'Another restaurant example'
      }
    ],
    zone: [
      {
        name: 'Downtown Food Zone',
        slug: 'downtown-food-zone',
        table: '3',
        url: '/z/downtown-food-zone/t/3',
        description: 'Food zone with multiple vendors'
      },
      {
        name: 'Mall Food Court',
        slug: 'mall-food-court',
        table: '8',
        url: '/z/mall-food-court/t/8',
        description: 'Shopping mall food court'
      }
    ],
    encoded: [
      {
        name: 'Encoded QR Example',
        slug: 'test-restaurant',
        table: '1',
        url: '/scan/eyJyZXN0YXVyYW50SWQiOiJ0ZXN0LXJlc3RhdXJhbnQiLCJyZXN0YXVyYW50U2x1ZyI6InRlc3QtcmVzdGF1cmFudCIsInJlc3RhdXJhbnROYW1lIjoiVGVzdCBSZXN0YXVyYW50IiwidGFibGVOdW1iZXIiOiIxIiwic2Vzc2lvbklkIjoidGVzdC1zZXNzaW9uIiwiY3JlYXRlZEF0IjoiMjAyNC0wMS0wMVQwMDowMDowMC4wMDBaIn0',
        description: 'Base64 encoded QR data'
      }
    ]
  };

  const handleTestQR = (qrCode) => {
    // Navigate to the QR URL to simulate scanning
    navigate(qrCode.url);
  };

  const currentQRCodes = testQRCodes[selectedType];

  return (
    <div className="min-h-screen bg-primary-bg py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-fredoka text-accent mb-4">
            QR Code Testing
          </h1>
          <p className="text-white/70 font-raleway text-lg">
            Test different QR code formats and scanning flows
          </p>
        </motion.div>

        {/* Type Selector */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-8"
        >
          <h2 className="text-xl font-fredoka text-white mb-4">Select QR Type</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => setSelectedType('restaurant')}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                selectedType === 'restaurant'
                  ? 'border-accent bg-accent/20 text-white'
                  : 'border-white/20 bg-white/5 text-white/70 hover:border-accent/50'
              }`}
            >
              <FaStore className="text-2xl mx-auto mb-2" />
              <div className="font-raleway font-semibold">Restaurant</div>
              <div className="text-sm opacity-70">Single restaurant QR</div>
            </button>

            <button
              onClick={() => setSelectedType('zone')}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                selectedType === 'zone'
                  ? 'border-accent bg-accent/20 text-white'
                  : 'border-white/20 bg-white/5 text-white/70 hover:border-accent/50'
              }`}
            >
              <FaMapMarkerAlt className="text-2xl mx-auto mb-2" />
              <div className="font-raleway font-semibold">Food Zone</div>
              <div className="text-sm opacity-70">Multi-vendor zone</div>
            </button>

            <button
              onClick={() => setSelectedType('encoded')}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                selectedType === 'encoded'
                  ? 'border-accent bg-accent/20 text-white'
                  : 'border-white/20 bg-white/5 text-white/70 hover:border-accent/50'
              }`}
            >
              <FaQrcode className="text-2xl mx-auto mb-2" />
              <div className="font-raleway font-semibold">Encoded</div>
              <div className="text-sm opacity-70">Base64 encoded data</div>
            </button>
          </div>
        </motion.div>

        {/* QR Code List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-4"
        >
          <h2 className="text-xl font-fredoka text-white mb-4">
            Test QR Codes - {selectedType.charAt(0).toUpperCase() + selectedType.slice(1)}
          </h2>
          
          {currentQRCodes.map((qrCode, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 * index }}
              className="bg-white/10 backdrop-blur-lg rounded-xl p-6 hover:bg-white/15 transition-all duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <FaQrcode className="text-accent text-xl" />
                    <h3 className="text-lg font-fredoka text-white">
                      {qrCode.name}
                    </h3>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-white/70 text-sm font-raleway mb-2">
                    <div className="flex items-center space-x-1">
                      <FaTable />
                      <span>Table {qrCode.table}</span>
                    </div>
                    <div>Slug: {qrCode.slug}</div>
                  </div>
                  
                  <p className="text-white/60 text-sm font-raleway mb-3">
                    {qrCode.description}
                  </p>
                  
                  <div className="bg-black/30 rounded-lg p-3 font-mono text-xs text-white/80 break-all">
                    {window.location.origin}{qrCode.url}
                  </div>
                </div>
                
                <button
                  onClick={() => handleTestQR(qrCode)}
                  className="ml-6 bg-accent hover:bg-accent/90 text-white px-6 py-3 rounded-lg font-raleway font-semibold flex items-center space-x-2 transition-colors"
                >
                  <span>Test Scan</span>
                  <FaArrowRight />
                </button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Instructions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mt-8"
        >
          <h3 className="text-lg font-fredoka text-white mb-4">How it works:</h3>
          <div className="space-y-2 text-white/70 font-raleway">
            <p>1. <strong>Restaurant QR:</strong> Direct access to single restaurant (format: /r/slug/t/table)</p>
            <p>2. <strong>Zone QR:</strong> Access to food zones with multiple vendors (format: /z/slug/t/table)</p>
            <p>3. <strong>Encoded QR:</strong> Base64 encoded data with full restaurant info (format: /scan/data)</p>
            <p>4. All formats automatically create customer sessions and redirect to the appropriate login page</p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default QRTestPage;
