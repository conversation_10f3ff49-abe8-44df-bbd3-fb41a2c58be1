import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaSearch, FaShoppingCart, FaStar, FaPlus, FaMinus } from 'react-icons/fa';
import { MdOutlineRamenDining } from "react-icons/md";
import Footer from '../../components/Footer';
import Navbar from '../customer_user/userNavbar';
import { CartContext } from '../../context/CartContext.jsx';

const categories = [
  { id: 'all', name: 'All' },
  { id: 'starters', name: 'Starters' },
  { id: 'mains', name: 'Mains' },
  { id: 'desserts', name: 'Desserts' },
];

const dishes = [
  { id: 'dish1', name: 'Spicy Chicken Wings', price: 12.99, image: '/src/assets/burger.jpg', category: 'starters', description: 'Crispy chicken wings tossed in a fiery hot sauce.' },
  { id: 'dish2', name: 'Classic Burger', price: 15.50, image: '/src/assets/burger.jpg', category: 'mains', description: 'Juicy beef patty with lettuce, tomato, and cheese.' },
  { id: 'dish3', name: 'Lemonade', price: 4.00, image: '/src/assets/burger.jpg', category: 'all', description: 'Refreshing homemade lemonade.' },
  { id: 'dish4', name: 'Chocolate Lava Cake', price: 8.75, image: '/src/assets/burger.jpg', category: 'desserts', description: 'Warm chocolate cake with a molten center.' },
  { id: 'dish5', name: 'Vegetable Spring Rolls', price: 9.50, image: '/src/assets/burger.jpg', category: 'starters', description: 'Crispy spring rolls filled with fresh vegetables.' },
  { id: 'dish6', name: 'Grilled Salmon', price: 22.00, image: '/src/assets/burger.jpg', category: 'mains', description: 'Perfectly grilled salmon with asparagus.' },
  { id: 'dish7', name: 'spicy Shawarma', price: 12.99, image: '/src/assets/burger.jpg', category: 'starters', description: 'Crispy chicken wings tossed in a fiery hot sauce.' },
  { id: 'dish8', name: 'Chocalate shake', price: 15.50, image: '/src/assets/burger.jpg', category: 'mains', description: 'Juicy beef patty with lettuce, tomato, and cheese.' },
  { id: 'dish9', name: 'panner puff', price: 4.00, image: '/src/assets/burger.jpg', category: 'all', description: 'Refreshing homemade lemonade.' },
  { id: 'dish10', name: 'chicken roll', price: 8.75, image: '/src/assets/burger.jpg', category: 'desserts', description: 'Warm chocolate cake with a molten center.' },
  { id: 'dish11', name: 'Vegetable salad', price: 9.50, image: '/src/assets/burger.jpg', category: 'starters', description: 'Crispy spring rolls filled with fresh vegetables.' },
  { id: 'dish12', name: 'Chicken popcorn', price: 22.00, image: '/src/assets/burger.jpg', category: 'mains', description: 'Perfectly grilled salmon with asparagus.' },
];

const DigitalMenuScreen = () => {
  const { restaurantName, userId } = useParams();
  const navigate = useNavigate();
  const { addToCart, cartItems, updateCartItemQuantity, removeFromCart } = useContext(CartContext);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCartPopup, setShowCartPopup] = useState(false);
  const [lastAddedItem, setLastAddedItem] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDish, setSelectedDish] = useState(null);
  const [showDetailPopup, setShowDetailPopup] = useState(false);

  useEffect(() => {
    localStorage.setItem('cartItems', JSON.stringify(cartItems));
  }, [cartItems]);

  // Filter dishes based on both category and search query
  const filteredDishes = dishes
    .filter(dish => selectedCategory === 'all' || dish.category === selectedCategory)
    .filter(dish => {
      if (!searchQuery.trim()) return true;
      const query = searchQuery.toLowerCase().trim();
      return (
        dish.name.toLowerCase().includes(query) ||
        dish.description.toLowerCase().includes(query) ||
        dish.category.toLowerCase().includes(query)
      );
    });

  // Calculate cart totals
  const cartTotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const cartItemCount = cartItems.reduce((count, item) => count + item.quantity, 0);

  // Handle adding item to cart
  const handleAddToCart = (dish) => {
    addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity: 1
    });
    setLastAddedItem(dish);

    // Show popup
    setShowCartPopup(true);

    // Hide popup after 3 seconds
    setTimeout(() => {
      setShowCartPopup(false);
    }, 3000);
  };

  // Handle incrementing item quantity
  const handleIncrementQuantity = (dish) => {
    const existingItem = cartItems.find(item => item.id === dish.id);
    if (existingItem) {
      updateCartItemQuantity(dish.id, existingItem.quantity + 1);
      setLastAddedItem(dish);
      
      // Show popup
      setShowCartPopup(true);
      
      // Hide popup after 3 seconds
      setTimeout(() => {
        setShowCartPopup(false);
      }, 3000);
    }
  };

  // Handle decrementing item quantity
  const handleDecrementQuantity = (dish) => {
    const existingItem = cartItems.find(item => item.id === dish.id);
    if (existingItem && existingItem.quantity > 1) {
      updateCartItemQuantity(dish.id, existingItem.quantity - 1);
    } else if (existingItem && existingItem.quantity === 1) {
      removeFromCart(dish.id);
    }
  };

  // Check if item is in cart
  const getItemQuantity = (dishId) => {
    const item = cartItems.find(item => item.id === dishId);
    return item ? item.quantity : 0;
  };

  // Navigate to cart
  const goToCart = () => {
    navigate(`/tableserve/${restaurantName}/${userId}/cart`);
  };

  // Handle dish click to show detail popup
  const handleDishClick = (dish) => {
    setSelectedDish(dish);
    setShowDetailPopup(true);
  };

  // Close detail popup
  const closeDetailPopup = () => {
    setShowDetailPopup(false);
  };

  return (
    <div className="min-h-screen bg-white text-black relative">
      <Navbar cartItemCount={cartItemCount} />

      {/* Search Bar */}
      <div className="relative mb-4 mt-8 px-8">
        <input
          type="text"
          placeholder="Search for dishes..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full  p-3 sm:p-2 pl-9 sm:pl-8 rounded-full bg-white border border-divider-border focus:border-none text-primary-bg text-base  placeholder-placeholder-subtext focus:outline-none focus:ring-2 focus:ring-accent shadow-md"
        />
        <FaSearch className="absolute left-11 sm:left-7 top-1/2 transform -translate-y-1/2 text-placeholder-subtext text-md " />
        {searchQuery && (
          <button 
            onClick={() => setSearchQuery('')}
            className="absolute right-6 sm:right-7 top-1/2 transform -translate-y-1/2 text-placeholder-subtext hover:text-accent"
          >
            ×
          </button>
        )}
      </div>
      <div className='flex gap-2 sm:gap-2 font-bold mb-4 sm:mb-2'>
        <MdOutlineRamenDining className='text-2xl sm:text-xl text-primar-bg ml-3 sm:ml-4' />
        <p className='text-md sm:text-base'>Hungry? Start Here.</p>
      </div>
      {/* Categories */}
      <div className="flex space-x-4 px-4  overflow-x-auto pb-2  scrollbar-hide whitespace-nowrap scroll-smooth mb-3 ">
        {categories.map(category => (
          <motion.button
            key={category.id}
            whileTap={{ scale: 0.95 }}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-6 sm:px-4 py-1 sm:py-1.5 rounded-full font-raleway text-[14px] sm:text-sm whitespace-nowrap scrollbar-hide scroll-smooth shadow-md
              ${selectedCategory === category.id ? 'bg-accent text-white' : 'bg-white text-primary-bg border border-divider-border'}`}
          >
            {category.name}
          </motion.button>
        ))}
      </div>

      {/* Dish List */}
      {filteredDishes.length > 0 ? (
        <div className="grid items-center justify-center grid-cols-2 p-2 sm:p-4 md:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3">
          {filteredDishes.map(dish => (
          <motion.div
            key={dish.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col"
          >
            <div className='relative text-primary-bg'>
              <img 
                src={dish.image} 
                className="w-full h-36 sm:h-32 md:h-40 lg:h-48 object-cover bg-white rounded-lg sm:rounded-xl shadow-md mb-1 sm:mb-2 cursor-pointer" 
                onClick={() => handleDishClick(dish)}
              />
              
            </div>
            <div className='flex justify-between mr-2'>
              <h3 className="text-base pt-1 sm:text-base font-raleway font-bold line-clamp-1 pl-1 sm:pl-2">{dish.name}</h3>
            </div>
            <p className="font-sans font-medium text-md  pl-1 sm:pl-2 mb-2">₹{dish.price.toFixed(2)}</p>
            {getItemQuantity(dish.id) > 0 ? (
              <div className="flex items-center mt-auto mb-4">
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleDecrementQuantity(dish)}
                  className="w-8 h-8  rounded-full bg-accent text-white flex items-center justify-center hover:bg-red-600 transition-colors duration-300 shadow-md"
                >
                  <FaMinus className="text-md" />
                </motion.button>
                <span className="mx-2 font-sans font-bold text-lg">{getItemQuantity(dish.id)}</span>
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleIncrementQuantity(dish)}
                  className="w-8 h-8 rounded-full bg-accent text-white flex items-center justify-center hover:bg-red-600 transition-colors duration-300 shadow-md"
                >
                  <FaPlus className="text-md" />
                </motion.button>
              </div>
            ) : (
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => handleAddToCart(dish)}
                className="mt-auto w-full mx-auto px-2  rounded-full mb-2  bg-accent text-white py-1.5 sm:py-2 font-raleway text-[16px] sm:text-xs font-bold hover:bg-red-600 transition-colors duration-300 shadow-md flex items-center justify-center"
              >
                <FaShoppingCart className="mr-1" /> Add
              </motion.button>
            )}
          </motion.div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center p-10">
          <p className="text-lg font-medium text-gray-600 mb-2">No dishes found</p>
          <p className="text-sm text-gray-500">Try a different search term or category</p>
        </div>
      )}

      {/* Persistent Cart Button */}
      {cartItems.length > 0 && !showCartPopup && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="fixed bottom-3 right-3 z-20"
        >
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={goToCart}
            className="bg-black text-white p-3 rounded-full shadow-lg flex items-center justify-center relative"
          >
            <FaShoppingCart className="text-lg" />
            <span className="absolute -top-2 -right-2 bg-black text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center shadow-sm">
              {cartItemCount}
            </span>
          </motion.button>
        </motion.div>
      )}
      
      {/* Cart Popup */}
      <AnimatePresence>
        {showCartPopup && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="fixed bottom-2 left-0 right-0 bg-accent text-white p-6 rounded-xl shadow-lg z-80 max-w-md mx-auto"
          >
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <FaShoppingCart className="mr-1.5" />
                <span className="font-bold  text-md">Added to cart!</span>
              </div>
              <span className="text-md font-sans mb-2 font-bold ">{cartItemCount} {cartItemCount === 1 ? 'item' : 'items'}</span>
            </div>

            {lastAddedItem && (
              <div className="flex items-center mb-4">
                <div className="w-9 h-9  bg-white rounded-full overflow-hidden mr-2 shadow-sm">
                  <img src={lastAddedItem.image} alt={lastAddedItem.name} className="w-full h-full object-cover" />
                </div>
                <div className="flex-1 ">
                  <p className="font-bold text-md truncate">{lastAddedItem.name}</p>
                </div>
                <p className="font-bold font-sans text-md">₹{lastAddedItem.price.toFixed(2)}</p>
              </div>
            )}

            <div className="flex justify-between items-center">
              <div>
                <p className="text-md sm:text-base font-sans font-bold ">Total: <span className="font-bold">₹{cartTotal.toFixed(2)}</span></p>
              </div>
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={goToCart}
                className="bg-white hover:text-accent/80 text-accent px-4 p-2  rounded-full text-lg font-bold shadow-md flex items-center justify-center"
              >
                <FaShoppingCart className="mr-1 text-2xl" /> View Cart
              </motion.button>
            </div>
          </motion.div>
        )}
        


        {/* Dish Detail Popup */}
        {showDetailPopup && selectedDish && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="fixed bottom-4 left-0 right-0 bg-accent text-white p-2 sm:p-3 rounded-2xl shadow-lg z-50 max-h-[90vh] overflow-y-auto max-w-md mx-auto"
          >
            <div className="flex justify-between items-start ">
              <div className="flex-1">
                <h2 className="text-lg pl-2 font-bold">{selectedDish.name}</h2>
                <p className="text-accent font-medium text-sm sm:text-lg">₹{selectedDish.price.toFixed(2)}</p>
              </div>
              <button 
                onClick={closeDetailPopup}
                className="text-3xl font-bold text-white hover:text-black mr-4 rounded-full "
              >
                ×
              </button>
            </div>

            <div className="mb-3 relative">
              <img 
                src={selectedDish.image} 
                alt={selectedDish.name} 
                className="w-full h-52 object-cover rounded-xl shadow-md" 
              />
             
            </div>

            <div className="mb-4">
              <h3 className="font-bold mb-0.5 sm:mb-1 text-xl">Description</h3>
              <p className="text-white text-[16px] ">{selectedDish.description}</p>
            </div>

            <div className="flex justify-between items-center">
              {getItemQuantity(selectedDish.id) > 0 ? (
                <div className="flex items-center">
                  <motion.button
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleDecrementQuantity(selectedDish)}
                    className="w-10 h-10 rounded-full bg-white text-accent flex items-center justify-center hover:bg-black hover:text-white transition-colors duration-300 shadow-md"
                  >
                    <FaMinus className="text-xs" />
                  </motion.button>
                  <span className="mx-3 sm:mx-4 font-bold text-xl sm:text-lg">{getItemQuantity(selectedDish.id)}</span>
                  <motion.button
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleIncrementQuantity(selectedDish)}
                    className="w-10 h-10 rounded-full bg-white text-accent flex items-center justify-center hover:bg-black hover:text-white transition-colors duration-300 shadow-md"
                  >
                    <FaPlus className="text-xs" />
                  </motion.button>
                </div>
              ) : (
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    handleAddToCart(selectedDish);
                    closeDetailPopup();
                  }}
                  className="flex-1 py-2 m-4 rounded-full bg-white text-accent text-lg font-bold hover:text-accent/80 transition-colors duration-300 shadow-md"
                >
                  Add to cart
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <Footer />
    </div>
  );
};

export default DigitalMenuScreen;