import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import LocalStorageService from '../../../services/LocalStorageService';
import OTPService from '../../../services/OTPService';
import PasswordManagementModal from '../../common/PasswordManagementModal';
import ImageUpload from '../../common/ImageUpload';
import {
  FaPlus,
  FaEdit,
  FaTrash,

  FaLock,
  FaUnlock,
  FaKey,
  FaSearch,
  FaFilter,
  FaDownload,
  FaCopy,
  FaStore,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaCheckCircle,
  FaTimesCircle,
  FaClock
} from 'react-icons/fa';
import SuperAdminLayout from '../SuperAdminLayout';

const RestaurantAccounts = () => {
  const [restaurants, setRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showForm, setShowForm] = useState(false);
  const [editingRestaurant, setEditingRestaurant] = useState(null);
  const [showPasswords, setShowPasswords] = useState({});
  const [selectedRestaurants, setSelectedRestaurants] = useState([]);

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    type: 'single', // 'single' or 'zone'
    ownerName: '',
    ownerEmail: '',
    ownerPhone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    cuisine: '',
    subscriptionPlan: 'basic',
    status: 'active',
    password: '', // Manual password input
    logo: null, // Restaurant logo
    coverImage: '',
    // Zone-specific fields
    operatingHours: {
      monday: { open: '09:00', close: '22:00', closed: false },
      tuesday: { open: '09:00', close: '22:00', closed: false },
      wednesday: { open: '09:00', close: '22:00', closed: false },
      thursday: { open: '09:00', close: '22:00', closed: false },
      friday: { open: '09:00', close: '22:00', closed: false },
      saturday: { open: '09:00', close: '23:00', closed: false },
      sunday: { open: '10:00', close: '22:00', closed: false }
    },
    paymentConfig: {
      upiId: '',
      paymentModel: 'shop-wise' // 'shop-wise' or 'split-pay'
    },
    // Table limits set by Super Admin
    maxTables: 10 // Default table limit
  });

  // Password management state
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordTarget, setPasswordTarget] = useState(null);

  useEffect(() => {
    // Load restaurants from localStorage
    const loadRestaurants = () => {
      const restaurants = LocalStorageService.getRestaurants();
      setRestaurants(restaurants);
      setLoading(false);
    };

    loadRestaurants();
  }, []);



  const filteredRestaurants = restaurants.filter(restaurant => {
    const matchesSearch = restaurant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      restaurant.ownerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      restaurant.cuisine.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || restaurant.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.password && !editingRestaurant) {
      alert('Password is required for new restaurants');
      return;
    }

    if (editingRestaurant) {
      // Update existing restaurant
      const updateData = { ...formData };
      delete updateData.password; // Don't update password through regular form
      const updatedRestaurant = LocalStorageService.updateRestaurant(editingRestaurant.id, updateData);
      if (updatedRestaurant) {
        const updatedRestaurants = restaurants.map(r =>
          r.id === editingRestaurant.id ? updatedRestaurant : r
        );
        setRestaurants(updatedRestaurants);
      }
    } else {
      // Create new restaurant
      const username = formData.name.toLowerCase().replace(/\s+/g, '_') + '_admin';

      const restaurantData = {
        ...formData,
        slug: formData.name.toLowerCase().replace(/\s+/g, '-'),
        loginCredentials: {
          username: username,
          password: formData.password,
          lastLogin: null
        },
        tables: 0,
        revenue: '₹0',
        orders: 0,
        lastActive: null
      };

      const newRestaurant = LocalStorageService.addRestaurant(restaurantData);
      setRestaurants([...restaurants, newRestaurant]);

      // Show success message with credentials and QR URL
      const qrUrl = `${window.location.origin}/${newRestaurant.slug}`;
      alert(`Restaurant created successfully!\n\nLogin Credentials:\nUsername: ${username}\nPassword: ${formData.password}\nPhone: ${formData.ownerPhone}\n\nCustomer QR Code URL:\n${qrUrl}`);
    }

    resetForm();
  };



  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      type: 'single',
      ownerName: '',
      ownerEmail: '',
      ownerPhone: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      cuisine: '',
      subscriptionPlan: 'basic',
      status: 'active',
      password: '',
      logo: null,
      coverImage: '',
      operatingHours: {
        monday: { open: '09:00', close: '22:00', closed: false },
        tuesday: { open: '09:00', close: '22:00', closed: false },
        wednesday: { open: '09:00', close: '22:00', closed: false },
        thursday: { open: '09:00', close: '22:00', closed: false },
        friday: { open: '09:00', close: '22:00', closed: false },
        saturday: { open: '09:00', close: '23:00', closed: false },
        sunday: { open: '10:00', close: '22:00', closed: false }
      },
      paymentConfig: {
        upiId: '',
        paymentModel: 'shop-wise'
      }
    });
    setEditingRestaurant(null);
    setShowForm(false);
  };

  const handleEdit = (restaurant) => {
    setFormData({
      name: restaurant.name,
      slug: restaurant.slug,
      description: restaurant.description,
      type: restaurant.type || 'single',
      ownerName: restaurant.ownerName,
      ownerEmail: restaurant.ownerEmail,
      ownerPhone: restaurant.ownerPhone,
      address: restaurant.address,
      city: restaurant.city,
      state: restaurant.state,
      zipCode: restaurant.zipCode,
      cuisine: restaurant.cuisine,
      subscriptionPlan: restaurant.subscriptionPlan,
      status: restaurant.status,
      logo: restaurant.logo,
      operatingHours: restaurant.operatingHours || {
        monday: { open: '09:00', close: '22:00', closed: false },
        tuesday: { open: '09:00', close: '22:00', closed: false },
        wednesday: { open: '09:00', close: '22:00', closed: false },
        thursday: { open: '09:00', close: '22:00', closed: false },
        friday: { open: '09:00', close: '22:00', closed: false },
        saturday: { open: '09:00', close: '23:00', closed: false },
        sunday: { open: '10:00', close: '22:00', closed: false }
      },
      paymentConfig: restaurant.paymentConfig || {
        upiId: '',
        paymentModel: 'shop-wise'
      }
    });
    setEditingRestaurant(restaurant);
    setShowForm(true);
  };

  const handleDelete = (restaurantId) => {
    if (window.confirm('Are you sure you want to delete this restaurant? This action cannot be undone.')) {
      const success = LocalStorageService.deleteRestaurant(restaurantId);
      if (success) {
        const updatedRestaurants = restaurants.filter(r => r.id !== restaurantId);
        setRestaurants(updatedRestaurants);
      }
    }
  };

  const toggleStatus = (restaurantId) => {
    const restaurant = restaurants.find(r => r.id === restaurantId);
    if (restaurant) {
      const newStatus = restaurant.status === 'active' ? 'suspended' : 'active';
      const updatedRestaurant = LocalStorageService.updateRestaurant(restaurantId, { status: newStatus });
      if (updatedRestaurant) {
        const updatedRestaurants = restaurants.map(r =>
          r.id === restaurantId ? updatedRestaurant : r
        );
        setRestaurants(updatedRestaurants);
      }
    }
  };



  const copyCredentials = (restaurant) => {
    const credentials = `Restaurant: ${restaurant.name}\nUsername: ${restaurant.loginCredentials.username}\nPassword: ${restaurant.loginCredentials.password}`;
    navigator.clipboard.writeText(credentials);
    alert('Credentials copied to clipboard!');
  };

  const handlePasswordManagement = (restaurant) => {
    setPasswordTarget(restaurant);
    setShowPasswordModal(true);
  };

  const handlePasswordUpdate = async (restaurantId, newPassword) => {
    try {
      const updatedRestaurant = LocalStorageService.updateRestaurant(restaurantId, {
        loginCredentials: {
          ...passwordTarget.loginCredentials,
          password: newPassword
        }
      });

      if (updatedRestaurant) {
        const updatedRestaurants = restaurants.map(r =>
          r.id === restaurantId ? updatedRestaurant : r
        );
        setRestaurants(updatedRestaurants);
      }
    } catch (error) {
      throw new Error('Failed to update password');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'status-success bg-green-500/20';
      case 'suspended': return 'status-error bg-red-500/20';
      case 'pending': return 'status-warning bg-yellow-500/20';
      default: return 'text-theme-text-tertiary bg-gray-500/20';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <FaCheckCircle />;
      case 'suspended': return <FaTimesCircle />;
      case 'pending': return <FaClock />;
      default: return <FaClock />;
    }
  };

  if (loading) {
    return (
      <SuperAdminLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 spinner-theme rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-primary font-raleway">Loading restaurants...</p>
          </div>
        </div>
      </SuperAdminLayout>
    );
  }

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-primary mb-2">Single Restaurant Owners</h1>
            <p className="text-theme-text-secondary font-raleway text-sm sm:text-base">Manage independent restaurant owners and their accounts</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto btn-primary px-4 py-2 rounded-lg font-raleway font-semibold flex items-center justify-center space-x-2"
          >
            <FaPlus />
            <span>Add Restaurant</span>
          </button>
        </div>

        {/* Filters and Search */}
        <div className="admin-card rounded-2xl p-4 sm:p-6">
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="relative flex-1">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-text-tertiary" />
                <input
                  type="text"
                  placeholder="Search restaurants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full input-theme rounded-lg pl-10 pr-4 py-2 focus:outline-none"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full sm:w-auto input-theme rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="suspended">Suspended</option>
                <option value="pending">Pending</option>
              </select>
            </div>

          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-secondary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-theme-text-secondary font-raleway text-sm">Total Restaurants</p>
                <p className="text-2xl font-fredoka text-blue-500">{restaurants.length}</p>
              </div>
              <FaStore className="text-3xl text-blue-400" />
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-secondary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-theme-text-secondary font-raleway text-sm">Active</p>
                <p className="text-2xl font-fredoka text-green-400">
                  {restaurants.filter(r => r.status === 'active').length}
                </p>
              </div>
              <FaCheckCircle className="text-3xl mt-4 text-green-400" />
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-secondary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-theme-text-secondary font-raleway text-sm">Pending</p>
                <p className="text-2xl font-fredoka text-yellow-400">
                  {restaurants.filter(r => r.status === 'pending').length}
                </p>
              </div>
              <FaClock className="text-3xl mt-4 text-yellow-400" />
            </div>
          </div>
          <div className="bg-secondary backdrop-blur-lg rounded-2xl p-6 border border-secondary">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-theme-text-secondary font-raleway text-sm">Suspended</p>
                <p className="text-2xl font-fredoka text-red-400">
                  {restaurants.filter(r => r.status === 'suspended').length}
                </p>
              </div>
              <FaTimesCircle className="text-3xl mt-4 text-red-400" />
            </div>
          </div>
        </div>

        {/* Restaurant List */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-secondary overflow-hidden p-4">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Restaurant</th>
                  <th className="text-left p-4  text-theme-text-secondary font-raleway font-semibold">Type</th>
                  <th className="text-left p-4  text-theme-text-secondary font-raleway font-semibold">Owner</th>
                  <th className="text-left p-4  text-theme-text-secondary font-raleway font-semibold">Contact</th>
                  <th className="text-left p-4  text-theme-text-secondary font-raleway font-semibold">Status</th>
                  <th className="text-left p-4  text-theme-text-secondary font-raleway font-semibold">Revenue</th>
                  <th className="text-left p-4  text-theme-text-secondary font-raleway font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredRestaurants.map((restaurant) => (
                  <motion.tr
                    key={restaurant.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="border-t border-white/10 hover:bg-white/5 transition-colors"
                  >
                    <td className="p-4">
                      <div className="flex items-center space-x-3">
                        <img
                          src={restaurant.logo}
                          alt={restaurant.name}
                          className="w-10 h-10 rounded-lg object-cover"
                        />
                        <div>
                          <h4 className="text-theme-text-secondary font-raleway font-medium">{restaurant.name}</h4>
                          <p className="text-theme-text-secondary font-raleway text-sm">{restaurant.cuisine}</p>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-raleway ${restaurant.type === 'single'
                        ? 'bg-blue-500/20 text-blue-400'
                        : 'bg-purple-500/20 text-purple-400'
                        }`}>
                        {restaurant.type === 'single' ? <FaStore /> : <FaMapMarkerAlt />}
                        <span className="capitalize">{restaurant.type === 'single' ? 'Restaurant' : 'Food Zone'}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-theme-text-secondary font-raleway">{restaurant.ownerName}</p>
                        <p className="text-theme-text-secondary font-raleway text-sm">{restaurant.city}, {restaurant.state}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-theme-text-secondary font-raleway text-sm">{restaurant.ownerPhone}</p>
                        <p className="text-theme-text-secondary font-raleway text-sm">{restaurant.ownerEmail}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-raleway ${getStatusColor(restaurant.status)}`}>
                        {getStatusIcon(restaurant.status)}
                        <span className="capitalize">{restaurant.status}</span>
                      </div>
                    </td>

                    <td className="p-4">
                      <div>
                        <p className="text-theme-text-secondary font-raleway font-medium">{restaurant.revenue}</p>
                        <p className="text-theme-text-secondary font-raleway text-sm">{restaurant.orders} orders</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEdit(restaurant)}
                          className="text-blue-400 hover:text-blue-300 p-2 rounded-lg hover:bg-blue-500/20 transition-colors"
                          title="Edit Restaurant"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => toggleStatus(restaurant.id)}
                          className={`p-2 rounded-lg transition-colors ${restaurant.status === 'active'
                            ? 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
                            : 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
                            }`}
                          title={restaurant.status === 'active' ? 'Suspend Account' : 'Activate Account'}
                        >
                          {restaurant.status === 'active' ? <FaLock /> : <FaUnlock />}
                        </button>

                        <button
                          onClick={() => handlePasswordManagement(restaurant)}
                          className="text-yellow-400 hover:text-yellow-300 p-2 rounded-lg hover:bg-yellow-500/20 transition-colors"
                          title="Change Password"
                        >
                          <FaKey />
                        </button>
                        <button
                          onClick={() => copyCredentials(restaurant)}
                          className="text-green-400 hover:text-green-300 p-2 rounded-lg hover:bg-green-500/20 transition-colors"
                          title="Copy Credentials"
                        >
                          <FaCopy />
                        </button>
                        <button
                          onClick={() => handleDelete(restaurant.id)}
                          className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-500/20 transition-colors"
                          title="Delete Restaurant"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {filteredRestaurants.length === 0 && (
            <div className="text-center py-12">
              <FaStore className="text-6xl text-theme-text-tertiary mx-auto mb-4" />
              <h3 className="text-xl font-fredoka text-theme-text-primary mb-2">
                {restaurants.length === 0 ? 'No Restaurants Added Yet' : 'No Restaurants Found'}
              </h3>
              <p className="text-theme-text-secondary font-raleway mb-4">
                {restaurants.length === 0
                  ? 'Start by adding your first restaurant to the platform'
                  : 'No restaurants match your current search criteria'
                }
              </p>
              {restaurants.length === 0 && (
                <button
                  onClick={() => setShowForm(true)}
                  className="btn-primary px-6 py-3 rounded-lg font-raleway font-semibold"
                >
                  Add First Restaurant
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Form Modal */}
      <AnimatePresence>
        {showForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="admin-card backdrop-blur-xl rounded-2xl border border-accent/20 p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-raleway font-semibold text-primary">
                  {editingRestaurant ? 'Edit Restaurant' : 'Add New Restaurant'}
                </h2>
                <button
                  onClick={resetForm}
                  className="text-white hover:text-primary-bg text-4xl bg-accent p-1 w-12 rounded-full"
                >
                  ×
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-primary font-raleway mb-2">Restaurant Name *</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter Restaurant Name'
                    />
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">
                      {formData.type === 'zone' ? 'Primary Cuisine *' : 'Cuisine Type *'}
                    </label>
                    <input
                      type="text"
                      value={formData.cuisine}
                      onChange={(e) => setFormData({ ...formData, cuisine: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      placeholder={formData.type === 'zone' ? 'e.g., Multi-Cuisine' : 'e.g., Italian'}
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-primary font-raleway mb-2">Phone Number *</label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                    required
                    placeholder='Enter Phone Number'
                  />
                </div>
                <div>
                  <label className="block text-primary font-raleway mb-2">Description*</label>
                  <input type='comment-area'
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                    required
                    placeholder='Enter Description'
                  />
                </div>
                {/* Logo Upload */}
                <div>
                  <ImageUpload
                    currentImage={formData.logo}
                    onImageChange={(imageUrl, file) => {
                      setFormData({ ...formData, logo: imageUrl });
                    }}
                    label="Restaurant Logo"
                    size="large"
                    shape="rounded"
                    className="mb-4"
                  />
                </div>




                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-primary font-raleway mb-2">Owner Name *</label>
                    <input
                      type="text"
                      value={formData.ownerName}
                      onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter Owner Name'
                    />
                  </div>
                  <div>
                    <label className="block text-primary font-raleway mb-2">Owner Email *</label>
                    <input
                      type="email"
                      value={formData.ownerEmail}
                      onChange={(e) => setFormData({ ...formData, ownerEmail: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter Owner Email'
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-primary font-raleway mb-2">Phone Number *</label>
                    <input
                      type="tel"
                      value={formData.ownerPhone}
                      onChange={(e) => setFormData({ ...formData, ownerPhone: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter Owner Phone'
                    />
                  </div>

                </div>

                <div>
                  <label className="block text-primary font-raleway mb-2">Address *</label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                    className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                    required
                    placeholder='Enter Address'
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-primary font-raleway mb-2">City *</label>
                    <input
                      type="text"
                      value={formData.city}
                      onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter City'
                    />
                  </div>
                  <div>
                    <label className="block text-primary font-raleway mb-2">State *</label>
                    <input
                      type="text"
                      value={formData.state}
                      onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter State'
                    />
                  </div>
                  <div>
                    <label className="block text-primary font-raleway mb-2">ZIP Code *</label>
                    <input
                      type="text"
                      value={formData.zipCode}
                      onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter ZIP Code'
                    />
                  </div>
                </div>

                {/* Password field - only for new restaurants */}
                {!editingRestaurant && (
                  <div>
                    <label className="block text-primary font-raleway mb-2">Login Password *</label>
                    <input
                      type="password"
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      placeholder="Enter secure password for restaurant login"
                      required
                      minLength="6"
                    />
                    <p className="text-secondary  text-sm mt-1">This password will be used for restaurant owner login</p>
                  </div>
                )}

                {/* Table Limit Configuration */}
                <div>
                  <label className="block text-primary font-raleway mb-2">Maximum Tables (QR Codes) *</label>
                  <input
                    type="number"
                    min="1"
                    max="200"
                    value={formData.maxTables}
                    onChange={(e) => setFormData({ ...formData, maxTables: parseInt(e.target.value) || 1 })}
                    className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                    placeholder="Enter maximum number of tables"
                    required
                  />
                  <p className="text-secondary text-sm mt-1">
                    {formData.type === 'zone'
                      ? 'Maximum number of tables/QR codes for the entire zone'
                      : 'Maximum number of tables/QR codes for this restaurant'
                    }
                  </p>
                </div>

                {/* Zone-specific fields */}
                {formData.type === 'zone' && (
                  <div className="space-y-6 p-4 bg-transparent rounded-lg border border-primary">
                    <h3 className="text-lg font-fredoka text-primary">Zone Configuration</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-primary font-raleway mb-2">UPI ID for Payments</label>
                        <input
                          type="text"
                          value={formData.paymentConfig.upiId}
                          onChange={(e) => setFormData({
                            ...formData,
                            paymentConfig: { ...formData.paymentConfig, upiId: e.target.value }
                          })}
                          className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                          placeholder="zone@upi"
                        />
                      </div>
                      <div>
                        <label className="block text-primary font-raleway mb-2">Payment Model</label>
                        <select
                          value={formData.paymentConfig.paymentModel}
                          onChange={(e) => setFormData({
                            ...formData,
                            paymentConfig: { ...formData.paymentConfig, paymentModel: e.target.value }
                          })}
                          className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        >
                          <option value="shop-wise">Shop-wise Payment</option>
                          <option value="split-pay">Split Payment</option>
                        </select>
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                  <div>
                    <label className="block text-primary font-raleway mb-2">Status</label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent placeholder-primary"
                    >
                      <option value="active">Active</option>
                      <option value="pending">Pending</option>
                      <option value="suspended">Suspended</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-end space-x-4 pt-6 border-t border-primary">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-6 py-2 text-primary rounded-lg  font-semibold hover:text-red-500 font-raleway transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-accent hover:bg-accent/90 text-white px-6 py-2 rounded-lg font-raleway font-semibold"
                  >
                    {editingRestaurant ? 'Update Restaurant' : 'Create Restaurant'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Password Management Modal */}
      <PasswordManagementModal
        isOpen={showPasswordModal}
        onClose={() => {
          setShowPasswordModal(false);
          setPasswordTarget(null);
        }}
        entity={passwordTarget}
        entityType="restaurant"
        onPasswordUpdate={handlePasswordUpdate}
        currentUserRole="superadmin"
      />
    </SuperAdminLayout>
  );
};

export default RestaurantAccounts;
