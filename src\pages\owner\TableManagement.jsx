import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  FaTable,
  FaPlus,
  FaEdit,
  FaTrash,
  FaQrcode,
  FaEye,
  FaUsers,
  FaClock,
  FaCheck,
  FaTimes,
  FaMapMarkerAlt
} from 'react-icons/fa';
import SingleRestaurantLayout from '../../components/owner/SingleRestaurantLayout';
import LocalStorageService from '../../services/LocalStorageService';

const TableManagement = () => {
  const { restaurantId } = useParams();
  const { user } = useSelector((state) => state.auth);
  const [tables, setTables] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingTable, setEditingTable] = useState(null);
  const [formData, setFormData] = useState({
    number: '',
    capacity: 4,
    location: '',
    status: 'available'
  });

  useEffect(() => {
    loadTables();
  }, [restaurantId]);

  const loadTables = () => {
    try {
      // Load tables from localStorage
      const savedTables = localStorage.getItem(`tables_${restaurantId}`);
      if (savedTables) {
        setTables(JSON.parse(savedTables));
      } else {
        // Create default tables
        const defaultTables = Array.from({ length: 12 }, (_, i) => ({
          id: i + 1,
          number: i + 1,
          capacity: Math.floor(Math.random() * 4) + 2, // 2-6 people
          location: `Section ${Math.ceil((i + 1) / 4)}`,
          status: 'available',
          qrGenerated: false,
          lastOccupied: null,
          currentOrder: null
        }));
        setTables(defaultTables);
        localStorage.setItem(`tables_${restaurantId}`, JSON.stringify(defaultTables));
      }
    } catch (error) {
      console.error('Error loading tables:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddTable = () => {
    setEditingTable(null);
    setFormData({
      number: tables.length + 1,
      capacity: 4,
      location: '',
      status: 'available'
    });
    setShowModal(true);
  };

  const handleEditTable = (table) => {
    setEditingTable(table);
    setFormData({
      number: table.number,
      capacity: table.capacity,
      location: table.location,
      status: table.status
    });
    setShowModal(true);
  };

  const handleSaveTable = () => {
    try {
      let updatedTables;

      if (editingTable) {
        // Update existing table
        updatedTables = tables.map(table =>
          table.id === editingTable.id
            ? { ...table, ...formData }
            : table
        );
      } else {
        // Add new table
        const newTable = {
          id: Date.now(),
          ...formData,
          qrGenerated: false,
          lastOccupied: null,
          currentOrder: null
        };
        updatedTables = [...tables, newTable];
      }

      setTables(updatedTables);
      localStorage.setItem(`tables_${restaurantId}`, JSON.stringify(updatedTables));
      setShowModal(false);
    } catch (error) {
      console.error('Error saving table:', error);
    }
  };

  const handleDeleteTable = (tableId) => {
    if (window.confirm('Are you sure you want to delete this table?')) {
      const updatedTables = tables.filter(table => table.id !== tableId);
      setTables(updatedTables);
      localStorage.setItem(`tables_${restaurantId}`, JSON.stringify(updatedTables));
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'occupied': return 'bg-red-500';
      case 'reserved': return 'bg-yellow-500';
      case 'cleaning': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'available': return 'Available';
      case 'occupied': return 'Occupied';
      case 'reserved': return 'Reserved';
      case 'cleaning': return 'Cleaning';
      default: return 'Unknown';
    }
  };

  if (loading) {
    return (
      <SingleRestaurantLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-primary font-raleway">Loading tables...</p>
          </div>
        </div>
      </SingleRestaurantLayout>
    );
  }

  return (
    <SingleRestaurantLayout>
      <div className="p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2">Table Management</h1>
            <p className="text-secondary font-raleway text-sm sm:text-base">Manage your restaurant tables and seating arrangements</p>
          </div>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAddTable}
            className="mt-4 sm:mt-0 btn-primary px-6 py-3 rounded-xl font-raleway font-semibold flex items-center space-x-2"
          >
            <FaPlus />
            <span>Add Table</span>
          </motion.button>
        </div>

        {/* Tables Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {tables.map((table) => (
            <motion.div
              key={table.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="admin-card rounded-2xl p-6 hover:border-theme-accent-primary/30 transition-all duration-300"
            >
              {/* Table Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-theme-accent-primary rounded-full flex items-center justify-center">
                    <FaTable className="text-theme-text-inverse text-lg" />
                  </div>
                  <div>
                    <h3 className="text-lg font-fredoka text-theme-text-primary">Table {table.number}</h3>
                    <p className="text-theme-text-secondary font-raleway text-sm">{table.capacity} seats</p>
                  </div>
                </div>
                <div className={`w-3 h-3 rounded-full ${getStatusColor(table.status)}`}></div>
              </div>

              {/* Table Info */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center space-x-2">
                  <FaMapMarkerAlt className="text-theme-text-tertiary" />
                  <span className="text-theme-text-secondary font-raleway text-sm">{table.location || 'No location set'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FaUsers className="text-theme-text-tertiary" />
                  <span className="text-theme-text-secondary font-raleway text-sm">Capacity: {table.capacity} people</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(table.status)}`}></div>
                  <span className="text-theme-text-secondary font-raleway text-sm">{getStatusText(table.status)}</span>
                </div>
              </div>

              {/* QR Code Status */}
              <div className="mb-4 p-3 bg-theme-bg-secondary rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-theme-text-primary font-raleway text-sm">QR Code</span>
                  {table.qrGenerated ? (
                    <span className="text-green-500 text-sm flex items-center">
                      <FaCheck className="mr-1" />
                      Generated
                    </span>
                  ) : (
                    <span className="text-theme-text-tertiary text-sm flex items-center">
                      <FaTimes className="mr-1" />
                      Not Generated
                    </span>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => handleEditTable(table)}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg font-raleway text-sm flex items-center justify-center space-x-1"
                >
                  <FaEdit />
                  <span>Edit</span>
                </button>
                <button
                  onClick={() => handleDeleteTable(table.id)}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg font-raleway text-sm flex items-center justify-center space-x-1"
                >
                  <FaTrash />
                  <span>Delete</span>
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {tables.length === 0 && (
          <div className="text-center py-12">
            <FaTable className="text-6xl text-theme-text-tertiary mx-auto mb-4" />
            <h3 className="text-xl font-fredoka text-theme-text-primary mb-2">No Tables Found</h3>
            <p className="text-theme-text-secondary font-raleway mb-4">Start by adding your first table.</p>
            <button
              onClick={handleAddTable}
              className="bg-theme-accent-primary hover:bg-theme-accent-hover text-theme-text-inverse px-6 py-3 rounded-xl font-raleway font-semibold"
            >
              Add Your First Table
            </button>
          </div>
        )}

        {/* Add/Edit Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="admin-card rounded-2xl p-6 w-full max-w-md"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-fredoka text-theme-text-primary">
                  {editingTable ? 'Edit Table' : 'Add New Table'}
                </h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-theme-text-tertiary hover:text-theme-text-primary"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">Table Number</label>
                  <input
                    type="number"
                    value={formData.number}
                    onChange={(e) => setFormData({ ...formData, number: parseInt(e.target.value) })}
                    className="w-full px-4 py-3 border border-theme-border-primary rounded-lg bg-theme-bg-primary text-theme-text-primary focus:outline-none focus:border-theme-accent-primary"
                  />
                </div>

                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">Capacity</label>
                  <input
                    type="number"
                    min="1"
                    max="12"
                    value={formData.capacity}
                    onChange={(e) => setFormData({ ...formData, capacity: parseInt(e.target.value) })}
                    className="w-full px-4 py-3 border border-theme-border-primary rounded-lg bg-theme-bg-primary text-theme-text-primary focus:outline-none focus:border-theme-accent-primary"
                  />
                </div>

                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">Location</label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    placeholder="e.g., Window side, Corner, Section A"
                    className="w-full px-4 py-3 border border-theme-border-primary rounded-lg bg-theme-bg-primary text-theme-text-primary focus:outline-none focus:border-theme-accent-primary"
                  />
                </div>

                <div>
                  <label className="block text-theme-text-primary font-raleway font-medium mb-2">Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    className="w-full px-4 py-3 border border-theme-border-primary rounded-lg bg-theme-bg-primary text-theme-text-primary focus:outline-none focus:border-theme-accent-primary"
                  >
                    <option value="available">Available</option>
                    <option value="occupied">Occupied</option>
                    <option value="reserved">Reserved</option>
                    <option value="cleaning">Cleaning</option>
                  </select>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowModal(false)}
                  className="flex-1 bg-theme-bg-secondary hover:bg-theme-bg-tertiary text-theme-text-primary py-3 rounded-lg font-raleway"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveTable}
                  className="flex-1 bg-theme-accent-primary hover:bg-theme-accent-hover text-theme-text-inverse py-3 rounded-lg font-raleway font-semibold"
                >
                  {editingTable ? 'Update' : 'Add'} Table
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </SingleRestaurantLayout>
  );
};

export default TableManagement;
