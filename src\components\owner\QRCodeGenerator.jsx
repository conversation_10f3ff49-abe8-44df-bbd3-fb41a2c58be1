import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import LocalStorageService from '../../services/LocalStorageService';
import {
  FaQrcode,
  FaDownload,
  FaEye,
  FaEyeSlash,
  FaRedo,
  FaCopy,
  FaCheck,
  FaTable,
  FaPrint,
  FaTrash
} from 'react-icons/fa';
import QRCode from 'qrcode';

const QRCodeGenerator = () => {
  const { user } = useSelector((state) => state.auth);
  const [tables, setTables] = useState([]);
  const [selectedTables, setSelectedTables] = useState([]);
  const [qrCodes, setQrCodes] = useState({});
  const [loading, setLoading] = useState(false);
  const [copiedUrl, setCopiedUrl] = useState(null);
  const [maxTables, setMaxTables] = useState(10); // Set by Super Admin
  const [restaurantData, setRestaurantData] = useState(null);

  useEffect(() => {
    // Load restaurant data to get table limit
    const loadRestaurantData = () => {
      const restaurant = LocalStorageService.getRestaurant(user?.restaurantId);
      if (restaurant) {
        setRestaurantData(restaurant);
        setMaxTables(restaurant.maxTables || 10);
      }
    };

    if (user?.restaurantId) {
      loadRestaurantData();
    }
  }, [user?.restaurantId]);

  useEffect(() => {
    // Load existing tables or create default ones based on maxTables limit
    if (maxTables > 0 && user?.restaurantId) {
      const savedTables = localStorage.getItem(`tables_${user?.restaurantId}`);
      if (savedTables) {
        const parsedTables = JSON.parse(savedTables);
        // Ensure we don't exceed the maxTables limit
        const limitedTables = parsedTables.slice(0, maxTables);
        setTables(limitedTables);

        // If we had to trim tables, save the updated list
        if (limitedTables.length < parsedTables.length) {
          localStorage.setItem(`tables_${user?.restaurantId}`, JSON.stringify(limitedTables));
        }
      } else {
        // Create default tables up to maxTables limit
        const defaultTables = Array.from({ length: maxTables }, (_, i) => ({
          id: i + 1,
          number: i + 1,
          status: 'active',
          qrGenerated: false,
          lastGenerated: null,
          sessionId: null
        }));
        setTables(defaultTables);
        localStorage.setItem(`tables_${user?.restaurantId}`, JSON.stringify(defaultTables));
      }
    }
  }, [user?.restaurantId, maxTables]);

  const generateSecureToken = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  };

  const generateQRData = (tableNumber) => {
    const qrData = {
      restaurantId: user?.restaurantId,
      restaurantSlug: user?.restaurantSlug || user?.restaurantName?.toLowerCase().replace(/\s+/g, '-'),
      restaurantName: user?.restaurantName,
      restaurantType: user?.restaurantType || 'single',
      tableNumber,
      sessionId: generateSecureToken(),
      createdAt: new Date().toISOString(),
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      version: '1.0'
    };

    // Encode data (not for security, just for clean URLs)
    const encodedData = btoa(JSON.stringify(qrData)).replace(/[+/=]/g, (m) => {
      return { '+': '-', '/': '_', '=': '' }[m];
    });

    // Generate table-based URL for restaurant
    const tableUrl = `${window.location.origin}/tableserve/restaurant/${user?.restaurantId}/table/${tableNumber}/menu`;

    return {
      data: qrData,
      encodedData,
      url: tableUrl,
      scanUrl: `${window.location.origin}/scan/${encodedData}` // Keep legacy support
    };
  };

  const generateQRCode = async (tableNumber) => {
    try {
      const qrInfo = generateQRData(tableNumber);

      // Generate QR code image
      const qrCodeDataURL = await QRCode.toDataURL(qrInfo.url, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      return {
        ...qrInfo,
        qrCodeImage: qrCodeDataURL
      };
    } catch (error) {
      console.error('QR generation error:', error);
      return null;
    }
  };

  const handleGenerateSelected = async () => {
    if (selectedTables.length === 0) return;

    setLoading(true);
    const newQrCodes = { ...qrCodes };
    const updatedTables = [...tables];

    for (const tableId of selectedTables) {
      const table = tables.find(t => t.id === tableId);
      if (table) {
        const qrInfo = await generateQRCode(table.number);
        if (qrInfo) {
          newQrCodes[tableId] = qrInfo;

          // Update table info
          const tableIndex = updatedTables.findIndex(t => t.id === tableId);
          updatedTables[tableIndex] = {
            ...updatedTables[tableIndex],
            qrGenerated: true,
            lastGenerated: new Date().toISOString(),
            sessionId: qrInfo.data.sessionId
          };
        }
      }
    }

    setQrCodes(newQrCodes);
    setTables(updatedTables);
    localStorage.setItem(`tables_${user?.restaurantId}`, JSON.stringify(updatedTables));
    localStorage.setItem(`qr_codes_${user?.restaurantId}`, JSON.stringify(newQrCodes));

    setLoading(false);
    setSelectedTables([]);
  };

  const handleSelectAll = () => {
    if (selectedTables.length === tables.length) {
      setSelectedTables([]);
    } else {
      setSelectedTables(tables.map(t => t.id));
    }
  };

  const toggleTableSelection = (tableId) => {
    setSelectedTables(prev =>
      prev.includes(tableId)
        ? prev.filter(id => id !== tableId)
        : [...prev, tableId]
    );
  };

  const copyToClipboard = async (url, tableId) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(tableId);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (error) {
      console.error('Copy failed:', error);
    }
  };

  const downloadQR = (qrInfo, tableNumber) => {
    const link = document.createElement('a');
    link.download = `table-${tableNumber}-qr.png`;
    link.href = qrInfo.qrCodeImage;
    link.click();
  };

  const downloadAllQRs = () => {
    selectedTables.forEach(tableId => {
      const qrInfo = qrCodes[tableId];
      const table = tables.find(t => t.id === tableId);
      if (qrInfo && table) {
        setTimeout(() => downloadQR(qrInfo, table.number), tableId * 100);
      }
    });
  };

  const printQRs = () => {
    const printWindow = window.open('', '_blank');
    const qrContent = selectedTables.map(tableId => {
      const qrInfo = qrCodes[tableId];
      const table = tables.find(t => t.id === tableId);
      if (!qrInfo || !table) return '';

      return `
        <div style="page-break-after: always; text-align: center; padding: 20px;">
          <h2>${user?.restaurantName}</h2>
          <h3>Table ${table.number}</h3>
          <img src="${qrInfo.qrCodeImage}" style="width: 200px; height: 200px;" />
          <p style="font-size: 12px; margin-top: 10px;">Scan to order</p>
          <p style="font-size: 10px; color: #666;">${qrInfo.url}</p>
        </div>
      `;
    }).join('');

    printWindow.document.write(`
      <html>
        <head>
          <title>QR Codes - ${user?.restaurantName}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; }
            @media print { 
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          ${qrContent}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  const toggleTableStatus = (tableId) => {
    const updatedTables = tables.map(table =>
      table.id === tableId
        ? { ...table, status: table.status === 'active' ? 'inactive' : 'active' }
        : table
    );
    setTables(updatedTables);
    localStorage.setItem(`tables_${user?.restaurantId}`, JSON.stringify(updatedTables));
  };

  const regenerateQR = async (tableId) => {
    const table = tables.find(t => t.id === tableId);
    if (!table) return;

    setLoading(true);
    const qrInfo = await generateQRCode(table.number);
    if (qrInfo) {
      setQrCodes(prev => ({ ...prev, [tableId]: qrInfo }));

      const updatedTables = tables.map(t =>
        t.id === tableId
          ? { ...t, lastGenerated: new Date().toISOString(), sessionId: qrInfo.data.sessionId }
          : t
      );
      setTables(updatedTables);
      localStorage.setItem(`tables_${user?.restaurantId}`, JSON.stringify(updatedTables));
    }
    setLoading(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl font-fredoka text-white">QR Code Generator</h1>
          <p className="text-white/70 font-raleway">Generate QR codes for your tables</p>
          <p className="text-white/50 font-raleway text-sm mt-1">
            Table Limit: {tables.length} / {maxTables} tables (Set by Admin)
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={handleSelectAll}
            className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg font-raleway transition-colors"
          >
            {selectedTables.length === tables.length ? 'Deselect All' : 'Select All'}
          </button>

          {selectedTables.length > 0 && (
            <>
              <button
                onClick={handleGenerateSelected}
                disabled={loading}
                className="px-4 py-2 bg-accent hover:bg-accent/90 text-white rounded-lg font-raleway font-semibold disabled:opacity-50 flex items-center space-x-2"
              >
                <FaQrcode />
                <span>Generate QRs ({selectedTables.length})</span>
              </button>

              <button
                onClick={downloadAllQRs}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-raleway flex items-center space-x-2"
              >
                <FaDownload />
                <span>Download</span>
              </button>

              <button
                onClick={printQRs}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-raleway flex items-center space-x-2"
              >
                <FaPrint />
                <span>Print</span>
              </button>
            </>
          )}
        </div>
      </div>

      {/* Tables Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {tables.map((table) => {
          const qrInfo = qrCodes[table.id];
          const isSelected = selectedTables.includes(table.id);

          return (
            <motion.div
              key={table.id}
              layout
              className={`bg-white/10 rounded-2xl p-6 border-2 transition-all duration-200 ${isSelected
                ? 'border-accent bg-accent/10'
                : 'border-white/10 hover:border-white/20'
                }`}
            >
              {/* Table Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => toggleTableSelection(table.id)}
                    className="w-4 h-4 text-accent rounded focus:ring-accent"
                  />
                  <div className="flex items-center space-x-2">
                    <FaTable className="text-accent" />
                    <span className="text-white font-raleway font-semibold">
                      Table {table.number}
                    </span>
                  </div>
                </div>

                <button
                  onClick={() => toggleTableStatus(table.id)}
                  className={`p-2 rounded-lg transition-colors ${table.status === 'active'
                    ? 'text-green-400 hover:bg-green-500/20'
                    : 'text-red-400 hover:bg-red-500/20'
                    }`}
                >
                  {table.status === 'active' ? <FaEye /> : <FaEyeSlash />}
                </button>
              </div>

              {/* QR Code Display */}
              {qrInfo ? (
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-lg">
                    <img
                      src={qrInfo.qrCodeImage}
                      alt={`QR Code for Table ${table.number}`}
                      className="w-full h-auto"
                    />
                  </div>

                  {/* QR Actions */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={qrInfo.url}
                        readOnly
                        className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-xs font-mono"
                      />
                      <button
                        onClick={() => copyToClipboard(qrInfo.url, table.id)}
                        className="p-2 bg-accent hover:bg-accent/90 text-white rounded-lg transition-colors"
                      >
                        {copiedUrl === table.id ? <FaCheck /> : <FaCopy />}
                      </button>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => downloadQR(qrInfo, table.number)}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg font-raleway text-sm flex items-center justify-center space-x-2"
                      >
                        <FaDownload className="text-xs" />
                        <span>Download</span>
                      </button>

                      <button
                        onClick={() => regenerateQR(table.id)}
                        disabled={loading}
                        className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg disabled:opacity-50"
                      >
                        <FaRedo className="text-sm" />
                      </button>
                    </div>
                  </div>

                  {/* QR Info */}
                  <div className="text-xs text-white/60 font-raleway">
                    <p>Generated: {new Date(table.lastGenerated).toLocaleDateString()}</p>
                    <p>Status: {table.status}</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FaQrcode className="w-16 h-16 text-white/40 mx-auto mb-4" />
                  <p className="text-white/60 font-raleway text-sm">
                    Select and generate QR code
                  </p>
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center">
            <div className="w-16 h-16 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-white font-raleway">Generating QR codes...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default QRCodeGenerator;
