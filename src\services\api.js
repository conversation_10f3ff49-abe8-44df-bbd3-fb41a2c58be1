import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  resetPassword: (email) => api.post('/auth/reset-password', { email }),
};

export const restaurantAPI = {
  getAll: () => api.get('/restaurants'),
  getById: (id) => api.get(`/restaurants/${id}`),
  create: (data) => api.post('/restaurants', data),
  update: (id, data) => api.put(`/restaurants/${id}`, data),
  delete: (id) => api.delete(`/restaurants/${id}`),
  generateQR: (id) => api.post(`/restaurants/${id}/generate-qr`),
};

export const menuAPI = {
  getByRestaurant: (restaurantId) => api.get(`/restaurants/${restaurantId}/menu`),
  createCategory: (restaurantId, data) => api.post(`/restaurants/${restaurantId}/categories`, data),
  createItem: (restaurantId, categoryId, data) => api.post(`/restaurants/${restaurantId}/categories/${categoryId}/items`, data),
  updateItem: (restaurantId, itemId, data) => api.put(`/restaurants/${restaurantId}/items/${itemId}`, data),
  deleteItem: (restaurantId, itemId) => api.delete(`/restaurants/${restaurantId}/items/${itemId}`),
};

export const orderAPI = {
  create: (data) => api.post('/orders', data),
  getByRestaurant: (restaurantId) => api.get(`/restaurants/${restaurantId}/orders`),
  updateStatus: (orderId, status) => api.put(`/orders/${orderId}/status`, { status }),
  getById: (orderId) => api.get(`/orders/${orderId}`),
};

export const analyticsAPI = {
  getRestaurantAnalytics: (restaurantId, period) => api.get(`/analytics/restaurant/${restaurantId}?period=${period}`),
  getPlatformAnalytics: (period) => api.get(`/analytics/platform?period=${period}`),
  exportReport: (type, params) => api.get(`/analytics/export/${type}`, { params }),
};

export default api;