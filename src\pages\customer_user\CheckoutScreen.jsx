import React, { useState, useContext, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaArrowLeft, FaMoneyBillWave, FaCreditCard, FaWallet, FaShoppingBag, FaCreditCard as FaPayment, FaCheckCircle } from 'react-icons/fa';
import { MdDeliveryDining, MdAccessTime } from 'react-icons/md';
import { CartContext } from '../../context/CartContext.jsx';
import UserNavbar from './userNavbar';

const CheckoutScreen = () => {
  const { restaurantName, userId } = useParams();
  const navigate = useNavigate();
  const { cartItems } = useContext(CartContext);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [estimatedDeliveryTime, setEstimatedDeliveryTime] = useState(null);
  
  // Calculate order totals
  const subtotal = cartItems.reduce((total, item) => total + item.price * item.quantity, 0);
  const taxes = subtotal * 0.1; // Example 10% tax
  const total = subtotal + taxes;

  // Set estimated delivery time (15-25 minutes from now)
  useEffect(() => {
    const now = new Date();
    const minDeliveryTime = new Date(now.getTime() + 15 * 60000); // 15 minutes from now
    const maxDeliveryTime = new Date(now.getTime() + 25 * 60000); // 25 minutes from now
    
    const formatTime = (date) => {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };
    
    setEstimatedDeliveryTime({
      min: formatTime(minDeliveryTime),
      max: formatTime(maxDeliveryTime)
    });
  }, []);

  const handleProceedToPayment = () => {
    setShowPaymentModal(true);
  };

  const handlePaymentSelection = (method) => {
    setSelectedPaymentMethod(method);
    setIsProcessingPayment(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessingPayment(false);
      setShowPaymentModal(false);
      navigate(`/tableserve/${restaurantName}/${userId}/order-success`);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-white text-primary-bg pb-8 font-raleway">
     
      
      <div className="p-4 max-w-md mx-auto">
        {/* Header */}
        <div className="flex items-center mb-6">
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={() => navigate(-1)}
            className="p-3 bg-accent rounded-full shadow-lg"
          >
            <FaArrowLeft className="text-xl text-text-main" />
          </motion.button>
          <h1 className="text-2xl font-bold text-accent ml-6">Checkout</h1>
        </div>
        
        {/* Checkout Progress */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-accent flex items-center justify-center mb-1">
                <FaShoppingBag className="text-white" />
              </div>
              <span className="text-xs text-accent font-medium">Cart</span>
            </div>
            <div className="flex-1 h-1 bg-gray-200 mx-2">
              <div className="h-full bg-accent" style={{ width: '100%' }}></div>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-accent flex items-center justify-center mb-1">
                <FaPayment className="text-white" />
              </div>
              <span className="text-xs text-accent font-medium">Payment</span>
            </div>
            <div className="flex-1 h-1 bg-gray-200 mx-2">
              <div className="h-full bg-gray-300" style={{ width: '0%' }}></div>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mb-1">
                <FaCheckCircle className="text-white" />
              </div>
              <span className="text-xs text-gray-500 font-medium">Complete</span>
            </div>
          </div>
        </div>

      {cartItems.length === 0 ? (
        <div className="text-center text-accent text-lg mt-10">Your cart is empty.</div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl shadow-2xl shadow-black/40 backdrop-blur-md p-4 mb-6"
        >
          <h2 className="text-xl font-bold text-accent mb-4">Order Summary</h2>
          
          {/* Cart Items */}
          <div className="mb-3 sm:mb-4">
            {cartItems.map(item => (
              <div key={item.id} className="flex items-center py-2 sm:py-3 border-b border-divider-border last:border-b-0">
                <img src={item.image} alt={item.name} className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg sm:rounded-xl mr-3 sm:mr-4 shadow-sm" />
                <div className="flex-1">
                  <h3 className="text-md font-semibold sm:text-lg text-black">{item.name}</h3>
                  <p className="text-black font-sans text-xs sm:text-sm">₹{item.price.toFixed(2)} x {item.quantity}</p>
                </div>
                <div className="text-accent font-bold font-sans text-sm sm:text-base">
                  ₹{(item.price * item.quantity).toFixed(2)}
                </div>
              </div>
            ))}
          </div>

          {/* Order Totals */}
          <div className="pt-3 sm:pt-4 mt-3 sm:mt-4 border-t border-divider-border space-y-1.5 sm:space-y-2">
            <div className="flex justify-between">
              <span className="text-black text-sm sm:text-base">Subtotal:</span>
              <span className="text-black font-sans text-sm sm:text-base">₹{subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-black text-sm sm:text-base">Taxes (10%):</span>
              <span className="text-black font-sans text-sm sm:text-base">₹{taxes.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-lg sm:text-xl font-bold mt-2">
              <span className="text-black">Total:</span>
              <span className="text-accent font-sans">₹{total.toFixed(2)}</span>
            </div>
          </div>

          {/* Delivery Details */}
          <div className="mt-5 bg-gray-100 p-5 rounded-lg sm:rounded-xl">
            <h3 className="text-base sm:text-lg font-bold text-accent mb-2 sm:mb-3">Delivery Details</h3>
            <div className="space-y-2 sm:space-y-3">
              <div className="flex items-center">
                <div className="w-9 h-9 rounded-full bg-accent/20 flex items-center justify-center mr-2 sm:mr-3">
                  <MdDeliveryDining className="text-accent text-xl sm:text-xl" />
                </div>
                <div>
                  <p className="text-black text-sm sm:text-base">Table Number: <span className="font-bold">A12</span></p>
                  <p className="text-xs sm:text-sm text-gray-600">Your order will be served at your table</p>
                </div>
              </div>
              
              {estimatedDeliveryTime && (
                <div className="flex items-center">
                  <div className="w-9 h-9 sm:w-8 sm:h-8 rounded-full bg-accent/20 flex items-center justify-center mr-2 sm:mr-3">
                    <MdAccessTime className="text-accent text-lg sm:text-xl" />
                  </div>
                  <div>
                    <p className="text-black text-sm sm:text-base">Estimated Delivery Time:</p>
                    <p className="text-xs font-sans sm:text-sm font-bold text-accent">{estimatedDeliveryTime.min} - {estimatedDeliveryTime.max}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {cartItems.length > 0 && (
        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={handleProceedToPayment}
          className="w-full bg-accent text-text-main py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-medium mt-5 sm:mt-6 shadow-md
                     hover:bg-hover-shade transition-colors duration-300 text-sm sm:text-base"
        >
          Proceed to Payment
        </motion.button>
      )}

      <AnimatePresence>
        {showPaymentModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50"
          >
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 50, opacity: 0 }}
              className="bg-white rounded-2xl p-4 w-full max-w-sm shadow-2xl shadow-black/40 backdrop-blur-md mx-auto"
            >
              {isProcessingPayment ? (
                <div className="py-8 flex flex-col items-center">
                  <div className="w-16 h-16 border-4 border-accent border-t-transparent rounded-full animate-spin mb-6"></div>
                  <h2 className="text-xl font-bold text-accent mb-2">Processing Payment</h2>
                  <p className="text-gray-600 text-center">Please wait while we process your payment...</p>
                </div>
              ) : (
                <>
                  <h2 className="text-xl font-bold text-accent mb-4">Select Payment Method</h2>
                  <p className="text-gray-600 mb-4">Choose your preferred payment option below</p>
                  
                  <div className="space-y-3">
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handlePaymentSelection('UPI')}
                      className="w-full flex items-center p-3 bg-gray-100 rounded-xl text-black font-raleway font-bold text-base
                                 hover:bg-gray-200 transition-colors duration-300 border border-transparent hover:border-accent shadow-md"
                    >
                      <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center mr-3">
                        <FaCreditCard className="text-accent text-lg" />
                      </div>
                      <div className="text-left">
                        <div>UPI</div>
                        <div className="text-xs font-normal text-gray-500">Pay using UPI apps</div>
                      </div>
                    </motion.button>
                    
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handlePaymentSelection('COD')}
                      className="w-full flex items-center p-3 bg-gray-100 rounded-xl text-black font-bold font-raleway text-base
                                 hover:bg-gray-200 transition-colors duration-300 border border-transparent hover:border-accent shadow-md"
                    >
                      <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center mr-3">
                        <FaMoneyBillWave className="text-accent text-lg" />
                      </div>
                      <div className="text-left">
                        <div>Cash on Delivery</div>
                        <div className="text-xs font-normal text-gray-500">Pay when your order arrives</div>
                      </div>
                    </motion.button>
                    
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handlePaymentSelection('Wallet')}
                      className="w-full flex items-center p-3 bg-gray-100 rounded-xl text-black font-bold font-raleway text-base
                                 hover:bg-gray-200 transition-colors duration-300 border border-transparent hover:border-accent shadow-md"
                    >
                      <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center mr-3">
                        <FaWallet className="text-accent text-lg" />
                      </div>
                      <div className="text-left">
                        <div>Wallet</div>
                        <div className="text-xs font-normal text-gray-500">Pay using digital wallet</div>
                      </div>
                    </motion.button>
                  </div>
                  
                  <div className="mt-6 flex space-x-3">
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setShowPaymentModal(false)}
                      className="flex-1 py-2.5 rounded-xl font-medium text-accent border border-accent
                                hover:bg-accent/10 transition-colors duration-300 text-sm sm:text-base shadow-md"
                    >
                      Cancel
                    </motion.button>
                    
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handlePaymentSelection('COD')}
                      className="flex-1 bg-accent text-text-main py-2.5 rounded-xl font-medium shadow-md
                                hover:bg-hover-shade transition-colors duration-300 text-sm sm:text-base"
                    >
                      Confirm
                    </motion.button>
                  </div>
                </>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      </div>
    </div>
  );
};

export default CheckoutScreen;