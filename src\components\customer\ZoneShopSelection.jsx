import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FaStore,
  FaStar,
  FaClock,
  FaUtensils,
  FaArrowRight,
  FaMapMarkerAlt
} from 'react-icons/fa';
import LocalStorageService from '../../services/LocalStorageService';

const ZoneShopSelection = () => {
  const { zoneId, tableId } = useParams();
  const navigate = useNavigate();
  const [zone, setZone] = useState(null);
  const [shops, setShops] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadZoneData = () => {
      try {
        // Get zone information
        const zoneData = LocalStorageService.getZoneById(zoneId);
        if (zoneData) {
          setZone(zoneData);
          // Get all shops in this zone
          const allShops = zoneData.shops || [];
          // Filter only active shops
          const activeShops = allShops.filter(shop => shop.status === 'active');
          setShops(activeShops);
        }
      } catch (error) {
        console.error('Error loading zone data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadZoneData();
  }, [zoneId]);

  const handleShopSelect = (shopId) => {
    // Navigate to the selected shop's menu with table context
    navigate(`/tableserve/zone/${zoneId}/table/${tableId}/shop/${shopId}/menu`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-raleway">Loading zone shops...</p>
        </div>
      </div>
    );
  }

  if (!zone) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-fredoka text-gray-800 mb-4">Zone Not Found</h1>
          <p className="text-gray-600 font-raleway">The requested zone could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-lg">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <FaMapMarkerAlt className="text-accent mr-2" />
              <span className="text-sm font-raleway text-gray-600">Table {tableId}</span>
            </div>
            <h1 className="text-3xl font-fredoka text-gray-800 mb-2">
              {zone.name}
            </h1>
            <p className="text-gray-600 font-raleway">
              Choose from our amazing food vendors
            </p>
          </div>
        </div>
      </div>

      {/* Shop Selection */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {shops.length === 0 ? (
          <div className="text-center py-12">
            <FaStore className="text-6xl text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-fredoka text-gray-600 mb-2">No Shops Available</h2>
            <p className="text-gray-500 font-raleway">
              There are currently no active shops in this zone.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {shops.map((shop, index) => (
              <motion.div
                key={shop.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => handleShopSelect(shop.id)}
              >
                {/* Shop Image */}
                <div className="h-48 bg-gradient-to-br from-accent/20 to-accent/40 relative overflow-hidden">
                  {shop.logo ? (
                    <img
                      src={shop.logo}
                      alt={shop.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <FaStore className="text-4xl text-accent" />
                    </div>
                  )}
                  
                  {/* Status Badge */}
                  <div className="absolute top-4 right-4">
                    <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-raleway">
                      Open
                    </span>
                  </div>
                </div>

                {/* Shop Info */}
                <div className="p-6">
                  <h3 className="text-xl font-fredoka text-gray-800 mb-2">
                    {shop.name}
                  </h3>
                  
                  <p className="text-gray-600 font-raleway text-sm mb-4 line-clamp-2">
                    {shop.description || 'Delicious food awaits you!'}
                  </p>

                  {/* Shop Stats */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <FaStar className="text-yellow-400 mr-1" />
                      <span className="text-sm font-raleway text-gray-600">
                        {shop.rating || '4.5'} ({shop.reviews || '50+'})</span>
                    </div>
                    
                    <div className="flex items-center">
                      <FaClock className="text-gray-400 mr-1" />
                      <span className="text-sm font-raleway text-gray-600">
                        {shop.prepTime || '15-20'} min
                      </span>
                    </div>
                  </div>

                  {/* Cuisine Type */}
                  <div className="flex items-center mb-4">
                    <FaUtensils className="text-accent mr-2" />
                    <span className="text-sm font-raleway text-gray-600">
                      {shop.cuisine || 'Multi-Cuisine'}
                    </span>
                  </div>

                  {/* Order Button */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-accent text-white py-3 rounded-xl font-raleway font-semibold flex items-center justify-center hover:bg-accent/90 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShopSelect(shop.id);
                    }}
                  >
                    View Menu
                    <FaArrowRight className="ml-2" />
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Zone Info Footer */}
      <div className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center">
            <p className="text-gray-500 font-raleway text-sm">
              You're ordering from Table {tableId} in {zone.name}
            </p>
            <p className="text-gray-400 font-raleway text-xs mt-1">
              {zone.address && `${zone.address}, ${zone.city}`}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZoneShopSelection;
