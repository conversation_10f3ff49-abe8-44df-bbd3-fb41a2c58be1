@import url('https://fonts.googleapis.com/css2?family=Fredoka+One&family=Raleway:wght@400;600&display=swap');
@import './styles/themes.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --navbar-height: 6rem;
    }

    body {
        @apply bg-primary-bg font-raleway;
    }

    /* Fix autofill styling for login forms */
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.1) inset !important;
        -webkit-text-fill-color: white !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        transition: background-color 5000s ease-in-out 0s;
    }

    /* For Firefox */
    input:-moz-autofill {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* For other browsers */
    input:autofill {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Additional autofill fixes for login forms */
    input[type="text"]:-webkit-autofill,
    input[type="password"]:-webkit-autofill,
    input[type="tel"]:-webkit-autofill,
    input[type="email"]:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset !important;
        -webkit-text-fill-color: white !important;
        caret-color: white !important;
    }

    /* Ensure placeholder text remains visible */
    input:-webkit-autofill::placeholder {
        color: rgba(255, 255, 255, 0.6) !important;
        opacity: 1 !important;
    }

    /* Force consistent styling for all autofill states */
    input:-webkit-autofill:focus {
        -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset !important;
        -webkit-text-fill-color: white !important;
        border: 1px solid #FF6B35 !important;
        /* accent color */
    }

    /* Ensure icons remain visible over autofill background */
    .relative input:-webkit-autofill+* {
        z-index: 10;
        position: relative;
    }

    /* Hide browser's default password reveal button */
    input[type="password"]::-ms-reveal,
    input[type="password"]::-ms-clear {
        display: none;
    }

    input[type="password"]::-webkit-credentials-auto-fill-button,
    input[type="password"]::-webkit-strong-password-auto-fill-button {
        display: none !important;
    }

    /* Hide Edge's password reveal button */
    input[type="password"]::-ms-reveal {
        display: none;
    }

    /* Hide Chrome's password reveal button */
    input[type="password"]::-webkit-reveal {
        display: none;
    }

    /* Additional browser-specific password reveal button hiding */
    input[type="password"]::-webkit-textfield-decoration-container {
        visibility: hidden;
    }

    /* Ensure our custom eye button has proper z-index */
    .relative button[type="button"] {
        z-index: 10;
        position: relative;
    }

    /* Ensure password field icons are properly positioned */
    .relative .absolute {
        z-index: 10;
    }

    /* Prevent browser password reveal button from interfering */
    input[type="password"]::-webkit-outer-spin-button,
    input[type="password"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Password field icon positioning - ensure proper containment */
    .relative div.absolute.inset-y-0 {
        display: flex !important;
        align-items: center !important;
        pointer-events: none !important;
        z-index: 10 !important;
    }

    .relative div.absolute.inset-y-0.right-0 {
        pointer-events: auto !important;
        z-index: 20 !important;
    }

    .relative div.absolute.inset-y-0 button {
        pointer-events: auto !important;
        z-index: 21 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Ensure input field has proper stacking */
    .relative input[type="password"],
    .relative input[type="text"] {
        position: relative !important;
        z-index: 1 !important;
    }

    /* Prevent any layout issues */
    .relative {
        position: relative !important;
        isolation: isolate !important;
    }

    /* Line clamp utility for text truncation */
    .line-clamp-2 {
        display: -webkit-box;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-1 {
        display: -webkit-box;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Hide scrollbar for horizontal scroll sections */
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Force proper stacking context for password fields */
    .relative {
        isolation: isolate;
    }

    /* Prevent any browser extensions from interfering */
    input[type="password"] {
        position: relative;
        z-index: 1;
    }
}